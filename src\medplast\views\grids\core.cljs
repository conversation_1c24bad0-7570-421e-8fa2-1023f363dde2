(ns medplast.views.grids.core
  (:require
   [clojure.string]
   [medplast.views.core :as views-core]
   [reagent.core :as r]
   [cljs-bean.core :refer [->clj ->js]]
   ["ag-grid-react" :refer [AgGridReact]]))

(defn get-cell-data [params]
  ; (js/console.log params)
  (->
   (.. params -value)
   ->clj))

(defn get-row-data [params]
  ; (js/console.log params)
  (->
   (.. params -data)
   ->clj))

(defn- add-classes-for-action-link [link]
  (assoc link :classes-to-add " px-1 border-[1px] border-blue-600/35 box-border rounded-sm"))

(defn make-action-cell-renderer [user links-fn]
  ; links-fn expected to take row-data (doc) and return a sequence of
  ; {:href href :text text} links
  (fn [params]
    (let [doc (get-row-data params)
          links (links-fn doc)
          links (map add-classes-for-action-link links)
          links-filtered
          (filter (partial views-core/should-render-link user) links)
          links links-filtered
          link-count (count links)
          _index-of-last-link (dec link-count)
          links-rendered-with-commas-interspersed
          (for [[_index link] (map-indexed vector links-filtered)]
            (let [{:keys [_href _text _on-click]} link]
              [:<>
               (views-core/render-link link)
               #_(if on-click
                   [views-core/on-click-link on-click text]
                   [views-core/href-link href text])
               #_(when-not (= index index-of-last-link)
                   [:span ", "])]))]
      (r/as-element
       (into
        [:div {:class "flex gap-x-1"}]
        links-rendered-with-commas-interspersed)))))

(defn safer-name [input] (when input (name input)))

(def default-row-class-rules
  {"opacity-30" (fn [^js/obj params]
                  (let [inactive (when-some [active (.. params -data -active)]
                                   (not active))
                        invalid  (when-some [invalid (.. params -data -invalid)]
                                   invalid)]
                    (or inactive invalid)))
   "line-through" (fn [^js/obj params]
                    (when-some [invalid (.. params -data -invalid)]
                      invalid))})

(def active-cell-class-rules {"bg-red-600/20" #(false? (.-value %))})

(def invalid-cell-class-rules {"bg-red-600/30" #(true? (.-value %))})

(defn table
  [{:keys [row-data-atom
           col-defs initial-column-config
           pagination-page-sizes
           pagination-page-size
           filter-fn-atom row-class-rules]}]
  (let [row-data-js-atom (r/reaction (->js @row-data-atom))
        column-state (when initial-column-config
                       #js {:state (->js initial-column-config)})
        row-class-rules (or row-class-rules default-row-class-rules)
        on-grid-ready (fn [^js params]
                        (when (some? params)
                          (let [^js api (.-api params)]
                            (when column-state
                              (.applyColumnState api column-state)))))
        filter-fn (when filter-fn-atom @filter-fn-atom)
        filter-fn (when filter-fn (fn [x] (filter-fn (->clj (.-data x)))))
        pagination-page-sizes (or pagination-page-sizes [25 50 75 100 200 300 500])
        pagination-page-size (or pagination-page-size 50)
        default-col-def {:suppressMovable true ; prevent columns from being dragged
                         }
        grid-options (->js {:rowData @row-data-js-atom
                            :rowHeight 64
                            :pagination true
                            :paginationPageSizeSelector (->js pagination-page-sizes)
                            :paginationPageSize (-> pagination-page-size)
                            :domLayout "autoHeight"
                            :enableCellTextSelection true
                            :rowClassRules row-class-rules
                            :ensureDomOrder true
                            :suppressMenuHide true
                            ;:columnMenu "new"
                            :columnMenu "legacy"
                            :columnDefs (->js col-defs)
                            :defaultColDef (->js default-col-def)
                            :onGridReady on-grid-ready
                            :isExternalFilterPresent #(some? filter-fn)
                            :doesExternalFilterPass filter-fn})]
    [:div {:class "ag-theme-quartz"}
     (r/create-element AgGridReact grid-options)]))

#_(defn- compacted-long-text-component [{:keys [long-text first-only]}]
    (r/with-let [collapsed-atom (r/atom true)]
      (let [first-only (or first-only false)
            substr-separator-component
            [:span " \u2B25 "]
            substrings
            (clojure.string/split long-text #"\n")
            substrings
            (if first-only
              [(first substrings)]
              substrings)
            components
            (for [substring substrings]
              [:span substring])
            components
            (interpose substr-separator-component components)
            multiple-comments
            (< 1 (count substrings))
            ellipsis
            (when multiple-comments
              [:<>
               [:span " \u2b25 "]
               [:span {:class views-core/link-css-classes} "..."]])
            collapsed-switch-fn
            #(swap! collapsed-atom not)
            should-show-collapsed-with-ellipsis
            (and multiple-comments @collapsed-atom)]
        [:div {:on-click collapsed-switch-fn}
         (if should-show-collapsed-with-ellipsis
           [:<>
            (first components)
            ellipsis]
           (apply vector :<> components))])))

(defn- compacted-long-text-component [{:keys [long-text first-only]}]
  (when (string? long-text)
    (r/with-let [collapsed-atom (r/atom true)]
      (let [long-text (clojure.string/trim long-text)
            lines (clojure.string/split long-text #"\n")
            first-line (first lines)
            first-only (or first-only false)
            lines (if first-only [first-line] lines)
            multiple-lines (< 1 (count lines))
            _first-only (not multiple-lines)
            ellipsis
            (when multiple-lines
              [:span {:class views-core/link-css-classes-static} "  ..."])
            collapsed-switch-fn #(swap! collapsed-atom not)
            should-show-single-line @collapsed-atom
            should-cursor-be-pointer (and multiple-lines @collapsed-atom)
            cursor-class (when should-cursor-be-pointer "cursor-pointer")]
        [:div {:class cursor-class :on-click collapsed-switch-fn}
         (if should-show-single-line
           [:<>
            [:span first-line]
            ellipsis]
           [:div {:class "whitespace-pre-wrap"} long-text])]))))

(defn get-field-value [params]
  (let [doc (get-row-data params)
        field-str (.. params -colDef -field)
        field-kw (keyword field-str)
        field-value (get doc field-kw)]
    field-value))

(defn compacted-long-text-cell-renderer [params]
  (let [field-value (get-field-value params)]
    (r/as-element
     [compacted-long-text-component {:long-text field-value}])))

(defn user-value-formatter [params]
  (let [user (->clj (.-value params))]
    (views-core/get-user-label user)))

(defn- user-cell-renderer-component [user]
  (let [href (views-core/get-href-for-user user)
        label (views-core/rich-user-label user)
        link [views-core/href-link href label]]
    [:div {:class "whitespace-nowrap"}
     (cond
       ;; Special marker for non-applicable fields
       (= user "-") [:span "-"]
       ;; Loading state
       (nil? user) [:span "Kraunama (arba nenustatyta)"]
       ;; User object with link
       (map? user) link
       ;; Fallback for other values
       :else user)]))

(defn user-cell-renderer [params]
  (let [user (->clj (.-value params))]
    (r/as-element [user-cell-renderer-component user])))

; (def user-cell-renderer (make-user-cell-renderer :salesperson))

(defn user-text-formatter
  [user-maybe]
  (->
   (let [user-maybe (->clj user-maybe)
         user (when (map? user-maybe) user-maybe)]
     (if user
       (views-core/get-user-label user)
       (let [filter-query (str user-maybe)]
         filter-query)))
   clojure.string/lower-case))

(defn- boolean-cell-component [value]
  [:span (if value "Taip" "Ne")])

(defn boolean-cell-renderer [params]
  (let [field-value (get-field-value params)]
    (r/as-element
     [boolean-cell-component field-value])))

(defn patient-value-formatter [params]
  (let [patient (.-value params)]
    (views-core/get-patient-label patient)))

(defn patient-text-formatter
  [patient-maybe]
  (->
   (let [patient-maybe (->clj patient-maybe)
         patient (when (map? patient-maybe) patient-maybe)]
     (if patient
       (views-core/get-patient-label patient)
       (let [filter-query (str patient-maybe)]
         filter-query)))
   clojure.string/lower-case))

(defn- patient-cell-renderer-component [patient]
  (let [href (views-core/get-href-for-patient patient)
        label (views-core/get-patient-label patient)
        link [views-core/href-link href label]]
    [:div {:class "text-balance divide-y [word-wrap:break-word]"}
     (cond
       ;; Special marker for non-applicable fields
       (= patient "-") [:span "-"]
       ;; Patient object with link
       patient link
       ;; Loading state
       :else [:span "Kraunama"])]))

(defn patient-cell-renderer [params]
  (let [patient (->clj (.-value params))]
    (r/as-element
     [patient-cell-renderer-component patient])))
