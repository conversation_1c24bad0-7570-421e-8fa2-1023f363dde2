## Core Goals & Requirements (Inferred)

Based on the project name ("medplast-crm"), file names (`patient.cljs`, `product.cljs`, `sale.cljs`, `user.cljs`, `audit.cljs`, `statistics.cljs`), and the presence of forms and data grids, the primary goal of this software appears to be providing a Customer Relationship Management (CRM) or business management system specifically tailored for a medical/plastics context.

Core requirements seem to include:
*   User authentication and management (`login.cljs`, `user.cljs`).
*   Managing patient data (`patient.cljs`).
*   Managing product data (`product.cljs`).
*   Recording and managing sales (`sale.cljs`).
*   Handling registrations, possibly for users or patients (`registration.cljs`, `preregistration.cljs`).
*   Displaying data in grids (`grids/` directory).
*   Providing forms for data entry (`forms/` directory).
*   Auditing capabilities (`audit.cljs`).
*   Displaying statistics (`statistics.cljs`).
*   Operating as a web-based application (`public/index.html`, `shadow-cljs.edn` targeting browser).