{"indexes": [{"collectionGroup": "sale", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "invalid", "order": "ASCENDING"}, {"fieldPath": "`sold-by`", "order": "ASCENDING"}]}, {"collectionGroup": "sale", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "invalid", "order": "ASCENDING"}, {"fieldPath": "`stock-expiration-date`", "order": "ASCENDING"}]}, {"collectionGroup": "sale", "queryScope": "COLLECTION", "fields": [{"fieldPath": "invalid", "order": "ASCENDING"}, {"fieldPath": "`stock-expiration-date`", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "sale", "fieldPath": "active", "ttl": false, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}]}