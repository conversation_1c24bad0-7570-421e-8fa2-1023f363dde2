(ns medplast.lang)

(defmacro <?
   "Like cljs.core.async/<!, but throws errors."
   [ch]
     `(medplast.lang/throw-when-err (cljs.core.async/<! ~ch)))

(defmacro go-try
  "Like cljs.core.async/go, but catches the first thrown error and returns it.
   
   Example:

   ```
   (defn read-both [ch-a ch-b]
     (go-try
       (let [a (<? ch-a)
             b (<? ch-b)]
         [a b])))
   ```

   This example function returns a channel conveying either a vector of a and b or one 
   of the errors conveyed by ch-a or ch-b. It will never read from ch-b if ch-a 
   returns an error.

   From: https://github.com/alexanderkiel/async-error/blob/master/src/async_error/core.cljc
   "
   [& body]
  `(cljs.core.async/go
     (try
       ~@body
       (catch js/Error e#
         e#))))
