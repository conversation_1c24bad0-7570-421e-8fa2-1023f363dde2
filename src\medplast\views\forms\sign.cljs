(ns medplast.views.forms.sign
  (:require ["@docuseal/react" :refer [DocusealForm]]
            [cljs-bean.core :refer [->js]]
            [medplast.firestore :as firestore]
            [medplast.state :as state]
            [medplast.views.core :as views-core :refer [header]]
            [reagent.core :as r]))

(defn sign-page
  "The Reagent component for the document signing page using Docuseal.
   It extracts the form template slug from route parameters and the patient ID from the state.
   It fetches patient data to pre-fill the form and gets the current user's information for metadata.
   It renders the `DocusealForm` component with the appropriate source URL, pre-fill data, metadata, and configuration options.
   Upon completion of the signing process, it navigates back.
   This page is needed to allow users to sign documents related to a patient."
  [match]
  (let [path-params (-> match :parameters :path)
        form-template-slug (:form-template-slug path-params)
        patient-id @state/route-match-doc-id-atom
        patients-col-ref @@(r/track state/get-implied-user-patient-col-ref-atom)
        doc-ref (firestore/document-ref patients-col-ref patient-id)
        patient @@(r/track firestore/get-doc-subscription-atom doc-ref)
        prefill (let [full-name (str (:first-name patient) " " (:last-name patient))]
                  {"Vardas pavardė" full-name})
        collecting-rep @@(r/track state/get-logged-in-user-atom)
        collecting-rep-id (:uid collecting-rep)
        collecting-rep-display-name (:display-name collecting-rep)
        collecting-rep-email (:email collecting-rep)
        metadata {:collecting-representative-id collecting-rep-id
                  :collecting-representative-display-name collecting-rep-display-name
                  :collecting-representative-email collecting-rep-email}]
    [:<>
     [header "Dokumento pasirašymas"
      {:on-click views-core/go-back! :text "Atgal"}]
     [:> DocusealForm
      {:src (str "https://docuseal.eu/d/" form-template-slug)
       :email collecting-rep-email
       :metadata (->js metadata)
       :role "Patient"
       :externalId patient-id
       :sendCopyEmail false
       :withDownloadButton false
       :withSendCopyButton false
       :allowToResubmit true
       :values (->js prefill)
       :rememberSignature false
       :onComplete
       views-core/go-back!
       }]]))
