(ns medplast.views.avatar
  (:require
   ; [reagent.core :as r]
   [random-avatar-generator
    :refer [generateRandomAvatarData getAvatarFromData]]
   ; [medplast.state :as state]
   ))

(defn get-avatar-data-uri [seed]
  (when seed
    (let [complexity 5
          separator "-"
          avatar-data (generateRandomAvatarData complexity separator seed)
          render-method "square"
          size 256
          avatar (getAvatarFromData avatar-data render-method size separator)
          encoded (js/btoa avatar)]
      (str "data:image/svg+xml;base64," encoded))))

(defn upward-triangle [svg-params]
  (let [svg-params (merge svg-params {:viewBox "0 0 100 100"})]
    [:svg svg-params
     [:polygon {:points "50,0 100,100 0,100"}]]))

(defn avatar-element [user]
  (let [uid (-> user :uid)
        seed uid
        is-admin (-> user :admin)]
    (when seed
      (let [left-seed seed
            ; right-seed (str seed 1)
            avatar-left (get-avatar-data-uri left-seed)
            ; avatar-right (get-avatar-data-uri right-seed)
            ]
        [:div {:class "flex flex-row gap-0 items-center"}
         [:img {:src avatar-left :class "w-3"}]
         #_[:img {:src avatar-right :class "w-3"}]
         (when is-admin
           [:div {:title "Administratorius"}
            [upward-triangle {:class "ml-0.5 fill-purple-700 size-2"}]])]))))
