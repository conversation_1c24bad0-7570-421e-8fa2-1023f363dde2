(ns medplast.views.forms.sale
  (:require
   [env]
   [medplast.lang :refer [parse-local-date] :refer-macros [go-try <?]]
   [medplast.views.forms.core :as forms-core :refer [form input labeled labeled-input checkbox]]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.state :as state]
   [medplast.data :as data]
   [medplast.firebase :as firebase]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]
   [cljc.java-time.temporal.chrono-unit :as chrono-unit]
   [cljc.java-time.local-date :as local-date]))

(defn- make-sure-doc-has-these-fields-set
  "Ensures a document map has `:assigned-to` and `:sold-by` keys set.
   If these keys are not already present in the `doc` map, they are set to the provided `user-uid`.
   This is important for tracking which user is responsible for a sale and who initially made the sale."
  [doc user-uid]
  (let [assigned-to (or (:assigned-to doc) user-uid)
        sold-by (or (:sold-by doc) user-uid)]
    (assoc doc :assigned-to assigned-to :sold-by sold-by)))

(defn- <?add-or-overwrite-sale!
  "Asynchronously adds or overwrites a sale document in Firestore.
   It first ensures the document has the correct assignee and seller set using `make-sure-doc-has-these-fields-set`.
   It then uses the implied user's sale collection reference to perform the add/overwrite operation.
   This function is used for saving sale data to the database."
  [doc]
  (let [sale-col-ref @@(r/track state/get-implied-user-sale-col-ref-atom)
        user-uid @@(r/track state/get-implied-user-uid-atom)
        doc (make-sure-doc-has-these-fields-set doc user-uid)]
    (firestore/<?add-or-overwrite-doc! sale-col-ref doc)))

(defn- <?add-or-update-and-redirect!
  "Handles the submission of a valid sale form for adding or updating a sale.
   It asynchronously adds or overwrites the sale document in Firestore using `<?add-or-overwrite-sale!` with the current form state.
   Upon successful submission, it navigates back using `views-core/go-back!`.
   This function is the default valid submit handler for the sale form when adding or editing."
  [form-state-atom]
  (go-try
   (<? (<?add-or-overwrite-sale! @form-state-atom))
   (views-core/go-back!)))

(defn- <?replace-and-redirect!
  "Handles the submission of a valid sale form for replacing an existing sale.
   It asynchronously adds or overwrites the new sale document, then sets the original sale document to inactive.
   Finally, it navigates back using `views-core/go-back!`.
   This function is used when a sale is being replaced by a new one, ensuring the old sale is marked as inactive."
  [form-state-atom]
  (let [doc @form-state-atom
        serialized-doc-ref (:replaces doc)
        sale-doc-ref (firestore/deserialize-doc-ref serialized-doc-ref)]
    (go-try
     (<? (<?add-or-overwrite-sale! doc))
     (<? (views-core/<?set-active-sale! sale-doc-ref false))
     (views-core/go-back!))))

; TODO refactor to make this readable
(defn input-handler
  "A custom input handler for stock quantity inputs within the stock selector.
   It reads the input value, converts it to a number, and updates the state atom at the specified state key (which is the product ID).
   It specifically handles empty input by removing the key from the state and ensures the value is at least 1 if not empty.
   This handler is needed to manage the dynamic stock quantities selected for a sale."
  [state-atom state-key ^js/Event e]
  (let [string-value (.-target.value e)
        number-but-nan-is-nil
        (if
         (empty? string-value)
          ""
          (.-target.valueAsNumber e))
        value number-but-nan-is-nil
        value (if
               (< value 1)
                ""
                value)]
    (if (= value "")
      (swap! state-atom dissoc state-key)
      (swap! state-atom assoc state-key value))))

; TODO add links so people can visit the product-view pages (maybe only in sale-view)
(defn- stock-selector-row
  "A Reagent component for rendering a single row in the stock selector, representing a product and its quantity input.
   It displays the product label and an input field for the quantity, bound to the state atom using a custom `input-handler`.
   Includes a link to the product view page (TODO: currently commented out).
   This component is a building block for the `stock-selector-type-section`."
  [& {:keys [product state-atom state-key read-only]}]
  (let [product-id (firestore/get-doc-id product)
        ; TODO would be nice to abstract away the need to use keywords
        product-id (keyword product-id)
        stock-cursor (r/cursor state-atom [state-key])
        href (views-core/get-href-for-product product)]
    [:li {:class "flex flex-row gap-2 items-center"}
     ; TODO streamline dbentity attribute definition and finding
     [:div {:class "grow"}
      [views-core/href-link href
       [views-core/product-label product]]]
     [:div {:class "flex-none w-20"}
      [input
       {:input-type :int
        :state-key product-id
        :state-atom stock-cursor
        :input-handler (partial input-handler stock-cursor product-id)
        :read-only read-only}]]]))

(defn- stock-selector-type-section
  "A Reagent component for rendering a section of the stock selector for a specific product type.
   It filters the list of products by the given `product-type`, sorts them, and renders a `stock-selector-row` for each product.
   Includes a labeled heading for the product type.
   This component is used within the `stock-selector` to group products by type."
  [& {:keys [product-type products state-atom state-key read-only]}]
  (let [products (filter #(= (:type %) product-type) products)
        products (sort-by #(str (:name %) (:variant-name %)) products)]
    (when #_{:clj-kondo/ignore [:not-empty?]} (not (empty? products))
          [:div {:class "flex flex-col gap-4"}
           [labeled {:label product-type}
            [:ul {:class "flex flex-col gap-1 pl-3"}
         ; BUG whenever an input gets changed, the whole component gets rerendered
         ; maybe split into multiple components
             (for [product products]
               (let [product-id (firestore/get-doc-id product)]
                 ^{:key product-id}
                 [stock-selector-row
                  :product product
                  :state-atom state-atom
                  :state-key state-key
                  :read-only read-only]))]]])))

; BUG sometimes on hot-reload these don't load and show up as No products found
(defn- stock-selector
  "A Reagent component for selecting stock quantities for a sale.
   It fetches the list of active products from Firestore using a subscription.
   It groups the products by type and renders a `stock-selector-type-section` for each type.
   Displays loading or not found messages while fetching products.
   This component provides the UI for selecting which products and how many of each are included in a sale."
  [& {:keys [state-atom state-key read-only #_required]}]
  ; NOTE gal geriau subscribinti? getas nehandlina firestore problemu,
  ;   nes jeigu neuzkrauna produktu saraso, tai reikia sakyti kad neuzkrauna viso add page'o
  ;   TODO gal tiesiog padaryti kad neuzsikrautu puslapis kol neuzsikrove produktai
  (r/with-let [products-atom
               (firestore/get-collection-atom
                (firestore/add-constraints
                 state/product-col-ref
                 [:where "active" "==" true]))]
    (let [products @products-atom
          product-types data/product-types]
      ; TODO factor out waiting/not-found logic
      (case products
        ::firebase/waiting [:span "Kraunami produktai."]
        (nil ()) [:span "Nerasta produktų."]
        [:div {:class "flex flex-col gap-4"}
         (for [product-type product-types]
           ^{:key product-type}
           [stock-selector-type-section
            :products products
            :product-type product-type
            :state-atom state-atom
            :state-key state-key
            :read-only read-only])]))))

(defn- sale-form
  "A Reagent component for rendering the sale form.
   It uses the generic `form` component and includes various labeled inputs, a patient selector, and a stock selector.
   It includes a watch (`forms-core/watch-inactive-validity-constraint`) to automatically set the sale as inactive if it's marked as invalid.
   It binds the form state to the provided `state-atom` and uses `<?add-or-update-and-redirect!` as the default valid submit function (can be overridden).
   Supports read-only mode, hiding certain fields using `:hide-set`, and making certain fields read-only using `:read-only-set`.
   This component provides the structure and fields for adding, viewing, and editing sale information."
  [{:keys [state-atom read-only hide-set read-only-set <?valid-fn]}]
  (r/with-let [remove-inactive-validity-constraint
               (forms-core/watch-inactive-validity-constraint state-atom read-only)]
    [form
     {:state-atom state-atom
      :read-only read-only
      :<?valid-fn (or <?valid-fn <?add-or-update-and-redirect!)}
     [labeled {:label "Pacientas" :hide-set hide-set :state-key :patient}
      [forms-core/patient-select
       {:state-key :patient
        :state-atom state-atom
        :read-only read-only
        :required true
        :read-only-set read-only-set
        :hide-set hide-set}]]
     [stock-selector
      :state-atom state-atom
      :state-key :stock
      :read-only read-only
      :required true]
     [labeled-input
      {:label "Sukūrimo data"
       :state-key :creation-date
       :input-type :date
       :state-atom state-atom
       :read-only true}]
     [labeled-input
      {:label "Pardavimo data"
       :state-key :sale-date
       :input-type :date
       :state-atom state-atom
       :read-only read-only
       :input-attrs {:required true}}]
     [labeled-input
      {:label "Prekių pasibaigimo data"
       :state-key :stock-expiration-date
       :input-type :date
       :state-atom state-atom
       :read-only read-only
       :input-attrs {:required true}}]
     [labeled {:label "Kas pardavė" :hide-set hide-set :state-key :sold-by}
      [forms-core/user-select
       {:state-key :sold-by
        :state-atom state-atom
        :read-only true
        :hide-set hide-set}]]
     [labeled {:label "Kam priskirta" :hide-set hide-set :state-key :assigned-to}
      [forms-core/user-select
       {:state-key :assigned-to
        :state-atom state-atom
        :read-only true
        :hide-set hide-set}]]
     [labeled {:label "Pardavimas kurį šis pakeičia" :hide-set hide-set :state-key :replaces}
      [forms-core/input
       {:state-key :replaces
        :state-atom state-atom
        :input-type :doc-ref
        :read-only true
        :hide-set hide-set
        :value-to-href-fn
        #(let [col-id-to-doc-id-map
               (-> %
                   firestore/deserialize-doc-ref
                   firestore/doc-ref-to-col-id-to-doc-id-map)
               user-id (get col-id-to-doc-id-map "user")
               sale-id (get col-id-to-doc-id-map "sale")]
           (rfe/href :medplast.routes/sale-view
                     {:user user-id :id sale-id}))}]]
     [labeled {:label "Komentarai"}
      [input
       {:state-key :comments
        :input-type :long-str
        :state-atom state-atom
        :read-only read-only}]]
     (let [read-only (if (:invalid @state-atom) true read-only)]
       [labeled {:label "Aktyvus" :hide-set hide-set :state-key :active}
        [checkbox
         {:state-key :active
          :state-atom state-atom
          :read-only read-only}]])
     [labeled {:label "Klaidingas" :hide-set hide-set :state-key :invalid}
      [checkbox
       {:state-key :invalid
        :state-atom state-atom
        :read-only read-only}]]]
    (finally (remove-inactive-validity-constraint))))

; NOTE not automatically propagated to replacement sales
(defn- get-initial-state
  "Returns a map defining the initial state for a new sale form.
   Includes default values for sale date, creation date, active status, and invalid status.
   This function is used to initialize the state atom when adding a new sale."
  []
  {:sale-date (str (local-date/now))
   :creation-date (str (local-date/now))
   :active true
   :invalid false})

(defn sale-add-page
  "The Reagent component for the sale add page.
   It initializes a state atom with the result of `get-initial-state`, potentially merging in a predefined patient ID from route query parameters.
   It renders the `sale-form` component with certain fields hidden.
   Includes a header with a back button.
   This page provides the UI for adding new sale information."
  []
  (r/with-let [initial-state (get-initial-state)
               match @state/route-match
               patient (-> match :query-params :patient)
               predefined-patient-state (when patient {:patient patient})
               initial-state (merge initial-state predefined-patient-state)
               state-atom (r/atom initial-state)]
    [:<>
     [header "Sukurti pardavimą"
      {:on-click views-core/go-back! :text "Atgal"}]
     [sale-form {:state-atom state-atom
                 :hide-set #{:replaces :sold-by :assigned-to :active :invalid}}]]))

(defn sale-view-page
  "The Reagent component for the sale view page.
   It fetches sale data based on the route parameter ID and user UID using a Firestore subscription.
   It displays the sale information using the `sale-form` in read-only mode.
   Includes a header with links to edit and replace the sale.
   This page is needed to display the details of a specific sale."
  [_match]
  (r/with-let [id-atom state/route-match-doc-id-atom
               doc-subscription-atom
               (r/reaction
                (when-let [sale-col-ref
                           (state/get-sale-col-ref-for-user
                            @state/route-match-user-uid-atom)]
                   @@(r/track
                      firestore/get-doc-subscription-atom
                      (firestore/document-ref sale-col-ref @id-atom))))]
    (let [id @id-atom
          doc @doc-subscription-atom]
      [:<>
       [header "Peržiūrėti pardavimą"
        {:on-click views-core/go-back! :text "Atgal"}
        {:href (rfe/href :medplast.routes/sale-edit
                         {:user @@(r/track state/get-implied-user-uid-atom)
                          :id id})
         :text "Redaguoti"}
        {:href (rfe/href :medplast.routes/sale-replace
                         {:user @@(r/track state/get-implied-user-uid-atom) :id id})
         :text "Pakeisti nauju"}]
       (case doc
         nil [:span "Nerasta ir/arba perkelta."]
         [sale-form {:state-atom doc-subscription-atom
                     :read-only true}])])))

(defn frozen?
  "Checks if a sale document is considered 'frozen' based on its last deactivated timestamp.
   A sale is frozen if the current time is beyond a certain duration (6 weeks in production, 10 seconds in development) after its `lastDeactivated` timestamp.
   This function is used to determine if a sale can still be edited or replaced."
  [sale]
  ; deals in ms
  (if-let [last-deactivation (:lastDeactivated sale)]
    (let [current-time (js/Date.now)
          one-second 1000
          one-minute (* one-second 60)
          one-hour (* one-minute 60)
          one-day (* one-hour 24)
          one-week (* one-day 7)
          freezing-duration (if env/dev (* one-second 10) (* one-week 6))
          start-of-frozen-period (+ last-deactivation freezing-duration)]
      (< start-of-frozen-period current-time))
    false))

(defn sale-edit-page
  "The Reagent component for the sale edit page.
   It fetches sale data based on the route parameter ID using a Firestore subscription.
   It determines if the sale is 'frozen' using `frozen?` and if the current user is an admin.
   The `sale-form` is rendered in read-only mode if the sale is frozen and the user is not an admin.
   Includes a header with links to view and replace the sale.
   This page allows editing existing sale information, with restrictions for frozen sales."
  [_match]
  (r/with-let [id-atom state/route-match-doc-id-atom
               doc-atom
               (r/reaction
                (when-let [sale-col-ref @@(r/track state/get-implied-user-sale-col-ref-atom)]
                  @@(r/track
                     firestore/get-doc-atom
                     (firestore/document-ref sale-col-ref @id-atom))))]
    (let [id @id-atom
          doc @doc-atom
          frozen (frozen? doc)
          admin (when frozen
                  (when-let [user @@(r/track state/get-logged-in-user-atom)]
                    (:admin user)))
          ; sale is read-only when it's frozen and this is not the admin
          read-only (and frozen (not admin))]
      [:<>
       [header "Redaguoti pardavimą"
        {:on-click views-core/go-back! :text "Atgal"}
        {:href (rfe/href :medplast.routes/sale-view
                         {:user @@(r/track state/get-implied-user-uid-atom) :id id})
         :text "Peržiūrėti"}
        {:href (rfe/href :medplast.routes/sale-replace
                         {:user @@(r/track state/get-implied-user-uid-atom) :id id})
         :text "Pakeisti nauju"}]
       (case doc
         nil [:span "Nerasta ir/arba perkelta."]
         [:<>
          (when frozen
            [:p {:class "text-red-400 font-semibold"}
             "Pardavimas užšaldytas; redaguoti gali tik administratoriai."])
          [sale-form {:state-atom (r/atom doc)
                      :read-only read-only}]])])))

(defn get-initial-replacement-sale-from-old-sale
  "Generates the initial state for a replacement sale based on an old sale document.
   It copies relevant fields from the old sale, calculates a new stock expiration date based on the old sale's stock life and the new sale date, and sets the `replaces` field to reference the old sale.
   This function is used when creating a new sale to replace an existing one, pre-populating the form with data from the old sale."
  [old-doc & {:keys [new-sale-date]}]
  ; NOTE behaves weird if stock-expiration-date is before sale-date
  ; NOTE uses supplied new-sale-date or `now`
  (let [old-sale-date (parse-local-date (:sale-date old-doc))
        old-stock-expiration-date (parse-local-date (:stock-expiration-date old-doc))
        old-stock-life-days
        (chrono-unit/between chrono-unit/days old-sale-date old-stock-expiration-date)
        new-creation-date (local-date/now)
        new-sale-date (or new-sale-date (local-date/now))
        new-stock-expiration-date
        (local-date/plus new-sale-date old-stock-life-days chrono-unit/days)]
    (-> old-doc
        firestore/remove-doc-id
        (dissoc :sold-by)
        (assoc :active true)
        (assoc :invalid false)
        (assoc :replaces (state/sale-to-serialized-doc-ref old-doc))
        (assoc :creation-date (str new-creation-date))
        (assoc :sale-date (str new-sale-date))
        (assoc :stock-expiration-date (str new-stock-expiration-date)))))

(defn sale-replace-page
  "The Reagent component for the sale replacement page.
   It fetches the old sale data using a Firestore subscription and generates the initial state for the new replacement sale using `get-initial-replacement-sale-from-old-sale`.
   It renders the `sale-form` component with the new sale data, hiding certain fields and making the patient field read-only.
   It uses `<?replace-and-redirect!` as the valid submit function to handle the replacement logic.
   Includes a header with links to view and edit the original sale.
   This page provides the UI for creating a new sale to replace an existing one."
  [_match]
  (r/with-let
    [old-doc-id-atom state/route-match-doc-id-atom
     new-doc-atom
     (r/reaction
      (when-let [sale-col-ref @@(r/track state/get-implied-user-sale-col-ref-atom)]
        (when-let [old-doc @@(r/track
                               firestore/get-doc-atom
                               (firestore/document-ref sale-col-ref @old-doc-id-atom))]
          (get-initial-replacement-sale-from-old-sale old-doc))))]
    (let [old-doc-id @old-doc-id-atom
          new-doc @new-doc-atom]
      [:<>
       [header "Pakeisti pardavimą nauju"
        {:on-click views-core/go-back! :text "Atgal"}
        {:href (rfe/href :medplast.routes/sale-view
                         {:user @@(r/track state/get-implied-user-uid-atom) :id old-doc-id})
         :text "Peržiūrėti"}
        {:href (rfe/href :medplast.routes/sale-edit
                         {:user @@(r/track state/get-implied-user-uid-atom) :id old-doc-id})
         :text "Redaguoti"}]
       [:p "Paspaudus \"Pateikti\", pirminis pardavimas yra deaktivuojamas, ir naujasis pardavimas (apibrėžtas žemiau) yra sukuriamas."]
       (case new-doc
         nil [:span "Nerasta ir/arba perkelta."]
         [sale-form {:state-atom (r/atom new-doc)
                     :hide-set #{:sold-by :assigned-to :active :invalid}
                     :read-only-set #{:patient}
                     :<?valid-fn <?replace-and-redirect!}])])))
