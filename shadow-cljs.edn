{:source-paths ["src"]
 :dependencies [; core.async bundled with shadow-cljs already
                ;[org.clojure/core.async "1.6.681"]
                [org.clojars.akiel/async-error "0.3"]
                [binaryage/devtools "1.0.7"]
                [nrepl "1.2.0-beta1"]
                [reagent "1.2.0"]
                [reagent-utils "0.3.8"]
                [cljs-bean "1.9.0"]
                [metosin/reitit-frontend "0.7.0"]
                [com.andrewmcveigh/cljs-time "0.5.2"]
                [com.widdindustries/cljc.java-time "0.1.21"]
                ; TODO PROD replace with [philoskim/debux-stubs "0.9.1"] in prod
                ;[philoskim/debux "0.9.1"]
                [mhuebert/shadow-env "0.1.6"]]
 :build-hooks [(shadow-env.core/hook)]
 :builds       {:app {:target     :browser
                      :output-dir "public/js"
                      :asset-path "/js"
                      :modules    {:app {:entries [medplast.core]}}
                      :devtools   {:after-load medplast.core/mount-root}
                      ; https://shadow-cljs.github.io/docs/UsersGuide.html#_release_specific_vs_development_configuration
                      ; https://cljs.github.io/api/compiler-options/closure-defines
                      ; https://cljs.github.io/api/cljs.core/goog-define
                      :dev {:compiler-options {:closure-defines {env/dev true}}}
                      :release {:compiler-options {:closure-defines {env/dev false}}}}}
 :dev-http {3000 {:root "public"
                  :host "0.0.0.0"}}}
