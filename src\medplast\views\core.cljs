(ns medplast.views.core
  (:require
   [clojure.string]
   [medplast.state :as state]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [medplast.views.avatar :as avatar]
   [reitit.frontend.easy :as rfe]
   [cljs-time.coerce]
   [cljs-time.core]
   [cljs-time.format]
   ; [debux.cs.core :refer-macros [clog clogn clogn_]]
   ))

(defn get-previous-breadcrumb []
  (let [match @state/route-match]
    (:previous-breadcrumb (:data match))))

#_(defn get-href-for-previous-breadcrumb []
    (let [previous-breadcrumb (get-previous-breadcrumb)]
      (when previous-breadcrumb
        (rfe/href previous-breadcrumb))))

(defn go-back! []
  #_(println "previous-breadcrumb" (get-previous-breadcrumb))
  (let [history-available (> (.-length js/history) 1)]
    (if history-available
      (.back js/history)
      (let [previous-breadcrumb (get-previous-breadcrumb)]
        (rfe/push-state previous-breadcrumb)))))

(def centering-inline-block-with-min-h
  " inline-flex items-center min-h-16 ")

(def link-css-classes-static
  " cursor-pointer text-blue-600 ")

(def link-css-classes
  (str
   link-css-classes-static
   centering-inline-block-with-min-h
   " min-w-12 "
   " active:bg-blue-800/10 hover:bg-blue-800/5 active:text-blue-800 active:scale-[98%] transition duration-100 "))

(defn href-link
  ([href content]
   [href-link href content nil])
  ([href content classes]
   [:a {:href href :class (str link-css-classes " " classes)} content]))

(defn on-click-link
  ([on-click content]
   (on-click-link on-click content nil))
  ([on-click content classes]
   [:a {:on-click on-click :class (str link-css-classes " " classes)} content]))

(defn should-render-link [user link]
  (let [claims-admin (-> user :admin)
        {:keys [when-fn admin-only]} link
        satisfies-when-fn (if (fn? when-fn) (when-fn) true)
        satisfies-admin-only (if admin-only claims-admin true)]
    (and satisfies-when-fn
         satisfies-admin-only)))

(defn render-link [{:keys [on-click href text classes-to-add]}]
  (if href
    [href-link href text classes-to-add]
    [on-click-link on-click text classes-to-add]))

(defn render-navigation-link [link]
  (let [utility-style (:utility-style link)
        base-classes " px-6 border-[1px] rounded-sm "
        classes (if utility-style
                 ;; Utility links have a different style - lighter border, different color
                 (str base-classes " border-gray-400 text-gray-600 ")
                 ;; Regular links keep the original style
                 (str base-classes " border-blue-600/75 "))]
    (render-link (assoc link :classes-to-add classes))))

(defn add-classes-for-header-link [header-link]
  (let [base-classes "px-2 border-[1px] rounded-sm "
        danger? (:danger header-link)
        extra-classes (if danger?
                        "border-red-600 text-red-700 hover:bg-red-100 "
                        "border-blue-600/75 ")
        combined-classes (str base-classes extra-classes)]
    (assoc header-link :classes-to-add combined-classes)))

(defn header [text & links]
  (r/with-let [user-atom @(r/track state/get-logged-in-user-atom)]
    (let [links (filter some? links)
          links (filter (partial should-render-link @user-atom) links)
          links (map add-classes-for-header-link links)]
      [:div {:class "flex flex-row gap-4 items-baseline flex-wrap"}
       [:h2 {:class (str "text-2xl font-bold" centering-inline-block-with-min-h)} text]
       (for [{:keys [text] :as link} links]
         ^{:key text} [:div (render-link link)])])))

(defn non-empty-trimmed-or-nil [s]
  (when s ; trims breaks on nil
    (let [trimmed (clojure.string/trim s)]
      (when-not (empty? trimmed)
        trimmed))))

(defn get-user-display-name [user]
  (-> user
      :display-name
      non-empty-trimmed-or-nil))

(defn get-user-label [{:keys [_display-name email] :as user}]
  (-> user
      get-user-display-name
      (or email)))

(defn product-label [product]
  [:<>
   [:span (:name product)]
   [:span {:class "italic"} " " (:variant-name product) " "]])

(defn product-label-with-type [product]
  [:span
   [:<>
    [:span {:class "text-gray-600"} (:type product)]
    [:span " "]
    [product-label product]]])

(defn get-href-for-product [product]
  (rfe/href :medplast.routes/product-view {:id (firestore/get-doc-id product)}))

(defn get-patient-label [{:keys [first-name last-name] :as _patient}]
  (let [first-name (non-empty-trimmed-or-nil first-name)
        last-name (non-empty-trimmed-or-nil last-name)]
    (if (and first-name last-name)
      (str last-name ", " first-name)
      (or last-name first-name))))

(defn get-user-uid-from-patient [patient]
  (:assigned-to patient))

(defn get-href-for-patient [patient]
  (let [user-uid (get-user-uid-from-patient patient)
        patient-uid (firestore/get-doc-id patient)]
    (rfe/href :medplast.routes/patient-view
              {:user user-uid
               :id patient-uid})))

(defn get-href-for-user [user]
  (rfe/href :medplast.routes/user-view {:id (firestore/get-doc-id user)}))

(defn get-user-uid-from-sale [sale]
  (:assigned-to sale))

(defn get-href-for-sale [sale]
  (let [sale-uid (firestore/get-doc-id sale)
        user-uid (get-user-uid-from-sale sale)]
    (rfe/href :medplast.routes/sale-view {:user user-uid :id sale-uid})))

(defn <?set-active! [sale-col-ref doc-id active]
  (let [partial-doc (firestore/set-doc-id {:active active} doc-id)]
    (firestore/<?partial-update-doc! sale-col-ref partial-doc)))

(defn <?set-active-sale! [sale-doc-ref active]
  (let [col-id-to-doc-id-map (firestore/doc-ref-to-col-id-to-doc-id-map sale-doc-ref)
        user-id (get col-id-to-doc-id-map "user")
        sale-id (get col-id-to-doc-id-map "sale")
        sale-col-ref (state/get-sale-col-ref-for-user user-id)]
    (<?set-active! sale-col-ref sale-id active)))

(defn rich-user-label [user]
  (let [display-name (get-user-display-name user)
        email (:email user)
        ; when display name is used, put email in the title
        title (when display-name email)
        user-label (or display-name email)]
    [:div {:class ""}
     [:span
      {:class "[word-break:break-word]"
       :title title}
      user-label " "]
     [:div {:class "inline-block"}
      [avatar/avatar-element user]]]))

(defn- parse-firebase-auth-dt-string-to-local-dt-string [datetime-string]
  (when datetime-string
    (let [utc-dt-str (js/Date. datetime-string)
          clj-dt (cljs-time.coerce/from-long (.getTime utc-dt-str))
          local-dt (cljs-time.core/to-default-time-zone clj-dt)
          formatter (cljs-time.format/formatter "yyyy-MM-dd HH:mm")
          local-dt-str (cljs-time.format/unparse formatter local-dt)]
      local-dt-str)))

(defn process-mfa-factor [factor]
  (if (:enrollment-time factor)
    (update factor :enrollment-time parse-firebase-auth-dt-string-to-local-dt-string)
    factor))

(defn process-user
  "Make sure user has non-nil `active` and `admin` fields, and make its timestamps frontend-oriented."
  [user]
  (let [active (:active user)
        active (if (nil? active) true active)
        admin (or (-> user :admin) false)
        mfa-factors (:mfa-enrolled-factors user)
        processed-mfa-factors (when (and mfa-factors (coll? mfa-factors))
                                (->> mfa-factors
                                     (map process-mfa-factor)
                                     ; has to be vector to be cursorable (reagent)
                                     vec))]
    (when user
      (-> user
          (update :creation-time parse-firebase-auth-dt-string-to-local-dt-string)
          (update :last-sign-in-time parse-firebase-auth-dt-string-to-local-dt-string)
          (assoc :active active)
          (assoc :admin admin)
          (assoc :mfa-enrolled-factors processed-mfa-factors)))))

; TODO use doc-ref instead of "sale-id"
; TODO de/serialize doc-ref
#_(defn deserialize-sale-id [s]
    (->clj s))

#_(defn serialize-sale [user-uid sale-id]
    (->clj [user-uid sale-id]))
