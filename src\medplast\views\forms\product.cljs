(ns medplast.views.forms.product
  (:require
    ; NOTE nebereikia
   [medplast.views.forms.core :refer [form input labeled select checkbox]]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.state :as state]
   [medplast.data :as data]
   [medplast.firebase :as firebase]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [medplast.lang :refer-macros [go-try <?]]
   [reitit.frontend.easy :as rfe]
   [cljc.java-time.local-date :as local-date]))

(defn- <?add-or-overwrite-product!
  "Asynchronously adds or overwrites a product document in the 'products' Firestore collection.
   It takes a product document map and uses the global product collection reference to perform the add/overwrite operation.
   This function is used for saving product data to the database."
  [doc]
  (firestore/<?add-or-overwrite-doc! state/product-col-ref doc))

(defn- <?handle-valid-submit!
  "Handles the submission of a valid product form.
   It asynchronously adds or overwrites the product document in Firestore using `<?add-or-overwrite-product!` with the current form state.
   Upon successful submission, it navigates back using `views-core/go-back!`.
   This function connects the product form's valid state to the action of saving the product data."
  [form-state-atom]
  (go-try
   (<? (<?add-or-overwrite-product! @form-state-atom))
   (views-core/go-back!)))

(defn- product-form
  "A Reagent component for rendering the product form.
   It uses the generic `form` component and includes various labeled inputs and a select for product details.
   It binds the form state to the provided `state-atom` and sets the `<?valid-fn` to `<?handle-valid-submit!`.
   Supports read-only mode and hiding certain fields using `:hide-set`.
   This component provides the structure and fields for both adding and editing product information."
  [{:keys [state-atom read-only hide-set]}]
  [form
   {:state-atom state-atom
    :read-only read-only
    :<?valid-fn <?handle-valid-submit!}
   [labeled {:label "Produkto pavadinimas"}
    [input
     {:state-key :name
      :input-type :str
      :state-atom state-atom
      :read-only read-only
      :input-attrs {:minLength 1
                    :required true}}]]
   [labeled {:label "Varianto pavadinimas"}
    [input
     {:state-key :variant-name
      :input-type :str
      :state-atom state-atom
      :read-only read-only
      :input-attrs {}}]]
   [labeled {:label "Rūšis"}
    [select
     {:state-key :type
      :state-atom state-atom
      :read-only read-only
      :prompt "Pasirinkite rūšį"
      :required true}
     (for [product-type data/product-types]
       {:value product-type :label product-type})]]
   [labeled {:label "Aprašymas"}
    [input
     {:state-key :description
      :input-type :long-str
      :state-atom state-atom
      :read-only read-only
      :input-attrs {}}]]
   [labeled {:label "Kiekis pakuotėje"}
    [input
     {:state-key :quantity-per-package
      :input-type :int
      :state-atom state-atom
      :read-only read-only
      :input-attrs {:min 1
                    :step 1
                    :required true}}]]
   [labeled {:label "Kiekis kompensuojamas per komensacijos laikotarpį"}
    [input
     {:state-key :quantity-compensated-per-period
      :input-type :int
      :state-atom state-atom
      :read-only read-only
      :input-attrs {:min 0
                    :step 1}}]]
   [labeled {:label "Kompensacijos periodas (mėnesiais)"}
    [input
     {:state-key :compensation-period-in-months
      :input-type :int
      :state-atom state-atom
      :read-only read-only
      :input-attrs {:min 0
                    :step 1}}]]
   [labeled {:label "Sukūrimo data"}
    [input
     {:state-key :addition-date
      :input-type :date
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Aktyvus" :hide-set hide-set :state-key :active}
    [checkbox
     {:state-key :active
      :state-atom state-atom
      :read-only read-only}]]])

(def initial-state
  "A map defining the initial state for a new product form.
   Includes default values for addition date, active status, and compensation period.
   This map is used to initialize the state atom when adding a new product."
  {:addition-date (str (local-date/now))
   :active true
   :compensation-period-in-months 1})

(defn product-add-page
  "The Reagent component for the product add page.
   It initializes a state atom with `initial-state` and renders the `product-form` component.
   It also includes a header with a back button.
   This page provides the UI for adding new product information."
  []
  (r/with-let [empty-state-atom (r/atom initial-state)]
    [:<>
     [header "Pridėti produktą"
      {:on-click views-core/go-back! :text "Atgal"}]
     [product-form {:state-atom empty-state-atom}]]))

(defn product-view-page
  "The Reagent component for the product view page.
   It fetches product data based on the route parameter ID using `firestore/get-doc-atom`.
   It displays the product information using the `product-form` in read-only mode.
   Includes a header with a back button and an 'Redaguoti' (Edit) link (admin only).
   This page is needed to display the details of a specific product."
  [_match]
  (let [id-atom state/route-match-doc-id-atom
        doc-atom
        @(r/track
          firestore/get-doc-atom
          (firestore/document-ref state/product-col-ref @id-atom))
        id @id-atom]
    [:<>
     [header "Peržiūrėti produktą"
      {:on-click views-core/go-back!
       :text "Atgal"}
      {:href (rfe/href :medplast.routes/product-edit {:id id})
       :admin-only true
       :text "Redaguoti"}]
     (if @doc-atom
       [product-form {:state-atom doc-atom :read-only true}]
       [:span "Kraunama arba nerasta..."])]))

(defn product-edit-page
  "The Reagent component for the product edit page.
   It fetches product data based on the route parameter ID using `firestore/get-doc-atom`.
   It displays the product information using the `product-form` in editable mode.
   Includes a header with a back button and a 'Peržiūrėti' (View) link.
   This page is needed to modify the details of an existing product."
  [_match]
  (let [id-atom state/route-match-doc-id-atom
        doc-atom
        @(r/track firestore/get-doc-atom (firestore/document-ref state/product-col-ref @id-atom))
        id @id-atom
        doc @doc-atom]
    [:<>
     [header "Redaguoti produktą"
      {:on-click views-core/go-back! :text "Atgal"}
      {:href (rfe/href :medplast.routes/product-view {:id id}) :text "Peržiūrėti"}]
     (case doc
       ::firebase/waiting [:span "Siunčiama..."]
       nil [:span "Nerasta."]
       [product-form {:state-atom doc-atom}])]))
