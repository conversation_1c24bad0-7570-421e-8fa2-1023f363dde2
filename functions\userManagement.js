// NOTE: in order for exported functions to be accessible to frontend, they have to be re-exported in index.js.
// NOTE: meant to be wrapped for use from clojurescript in the medplast.cloud-fn namespace.

const admin = require("firebase-admin");
const https = require("firebase-functions/v2/https");
const { getAuth } = require("firebase-admin/auth");

const { callerMustBeAdmin, validateNonEmptyStrings, regionOpts, _reflectUsers, db } = require("./common.js");

// --- NEW HELPER FUNCTIONS ---

/**
 * Validates that a UID is a non-empty string.
 * @param {string} uid - The User ID.
 * @throws {https.HttpsError} If validation fails.
 */
function _validateUid(uid) {
  if (!uid || typeof uid !== 'string') {
    throw new https.HttpsError("invalid-argument", "User ID (uid) is required and must be a string.");
  }
}

/**
 * Validates that a value is a string, and optionally non-empty.
 * @param {string} value - The value to validate.
 * @param {string} name - The name of the field for error messages.
 * @param {boolean} [canBeEmpty=false] - Whether the string can be empty.
 * @throws {https.HttpsError} If validation fails.
 */
function _validateString(value, name, canBeEmpty = false) {
    if (typeof value !== 'string' || (!canBeEmpty && value.trim() === '')) {
        throw new https.HttpsError("invalid-argument", `${name} must be a non-empty string.`);
    }
}

/**
 * Validates that a phone number is an E.164 formatted string.
 * @param {string} phoneNumber - The phone number.
 * @param {string} [fieldName="phone number"] - The name of the field for error messages.
 * @throws {https.HttpsError} If validation fails.
 */
function _validatePhoneNumber(phoneNumber, fieldName = "phone number") {
  if (typeof phoneNumber !== 'string' || !phoneNumber.startsWith('+')) {
    throw new https.HttpsError("invalid-argument", `Invalid '${fieldName}' format. It must be an E.164 string (e.g., +1234567890).`);
  }
}

/**
 * Handles Firebase Auth errors and converts them to HttpsError.
 * @param {Error} error - The error object from Firebase Auth.
 * @param {string|null} [uid=null] - Optional UID for context in error messages.
 * @param {string} [operation="operation"] - Optional operation name for context.
 * @returns {https.HttpsError} The HttpsError instance.
 */
function _handleAuthError(error, uid = null, operation = "operation") {
  const uidMsg = uid ? ` for user ${uid}` : "";
  // Log the original error code and message for server-side debugging
  console.error(`Auth error during ${operation}${uidMsg}: Code: ${error.code}, Message: ${error.message}`, error);

  if (error instanceof https.HttpsError) {
    return error; // If it's already an HttpsError, return it
  }

  switch (error.code) {
    case 'auth/email-already-exists':
      return new https.HttpsError("already-exists", `The email address is already in use by another account.`);
    case 'auth/invalid-phone-number':
      return new https.HttpsError("invalid-argument", `The provided phone number is invalid${uidMsg}.`);
    case 'auth/user-not-found':
      return new https.HttpsError("not-found", `User with ID ${uid} not found.`);
    case 'auth/weak-password':
      return new https.HttpsError("invalid-argument", `The new password is too weak or does not meet complexity requirements.`);
    case 'auth/invalid-password':
      return new https.HttpsError("invalid-argument", `The provided password does not meet security requirements (e.g., too short, malformed).`);
    case 'auth/multi-factor-auth-required':
    case 'auth/multi-factor-info-not-found':
      return new https.HttpsError("failed-precondition", `MFA configuration issue during ${operation}${uidMsg}. Details: ${error.message}`);
    case 'auth/requires-recent-login':
        return new https.HttpsError("failed-precondition", `This operation is sensitive and requires recent authentication. Please sign out and sign back in. Operation: ${operation}${uidMsg}.`);
    default:
      if (error.code && error.code.startsWith('auth/')) {
        return new https.HttpsError("failed-precondition", `An authentication error occurred during ${operation}${uidMsg}. Details: ${error.message}`);
      }
      return new https.HttpsError("internal", `An unexpected error occurred during ${operation}${uidMsg}. Details: ${error.message}`);
  }
}

const phoneFactorUid = 'phone-factor1';

/**
 * Generic helper for admin to update a user property.
 * @param {object} request - The HTTPS callable request object.
 * @param {string} uid - The UID of the user to update.
 * @param {string} propertyName - The name of the property to update (e.g., "email", "phoneNumber").
 * @param {*} newValue - The new value for the property.
 * @param {function} [validationFnForNewValue] - Optional validation function for the new value.
 * @returns {Promise<object>} Success message.
 * @throws {https.HttpsError} If update fails.
 */
async function _adminUpdateUserProperty(request, uid, propertyName, newValue, validationFnForNewValue) {
  _validateUid(uid);

  if (validationFnForNewValue) {
    validationFnForNewValue(newValue, `new${propertyName.charAt(0).toUpperCase() + propertyName.slice(1)}`);
  }

  const updatePayload = { [propertyName]: newValue };

  if (propertyName === "email") {
    updatePayload.emailVerified = true;
  } else if (propertyName === "phoneNumber") {
    // Specific validation for phone number is expected to be done by validationFnForNewValue
    updatePayload.multiFactor = {
      enrolledFactors: [
        {
          uid: phoneFactorUid,
          factorId: 'phone', // Firebase assigns factor UIDs, so 'phone' is a type identifier
          phoneNumber: newValue,
          displayName: 'Primary Phone',
        }
      ]
    };
  }
  // For displayName, validation (string or null) is handled by validationFnForNewValue

  try {
    console.info(`Admin ${request.auth.uid} attempting to change ${propertyName} for user ${uid} to "${newValue}"`);
    await admin.auth().updateUser(uid, updatePayload);
    await _reflectUsers();
    console.info(`Successfully changed ${propertyName} for user ${uid} and reflected to Firestore.`);
    return { success: true, message: `User ${propertyName} updated successfully.` };
  } catch (error) {
    throw _handleAuthError(error, uid, `update user ${propertyName}`);
  }
}


// --- REFACTORED EXISTING userManagement.js functions ---
/**
 * Validates the input for creating a new admin user.
 * @param {string} email - The email for the new user.
 * @param {string} displayName - The display name for the new user.
 * @param {string} phoneNumber - The phone number (E.164 format) for 2FA.
 * @throws {https.HttpsError} If validation fails.
 */
function _validateAdminCreateUserInput(email, displayName, phoneNumber) {
  // Assuming validateNonEmptyStrings from common.js throws HttpsError or similar
  // If not, replace with _validateString for each. For now, keep it if it's established.
  // validateNonEmptyStrings(email, displayName, phoneNumber); // Kept if it's robust
  _validateString(email, "email");
  _validateString(displayName, "displayName");
  _validatePhoneNumber(phoneNumber, "phoneNumber");
}

/**
 * Creates a new Firebase Authentication user.
 * @param {string} email - The email for the new user.
 * @param {string} displayName - The display name for the new user.
 * @param {string} phoneNumber - The phone number (E.164 format) for the user.
 * @returns {Promise<admin.auth.UserRecord>} The created user record.
 * @throws {https.HttpsError} If user creation fails.
 */
async function _createAuthUser(email, displayName, phoneNumber) {
  try {
    const userRecord = await admin.auth().createUser({
      email: email,
      emailVerified: true, // New users created by admin are verified by default.
      phoneNumber: phoneNumber,
      displayName: displayName,
      disabled: false,
      multiFactor: {
        enrolledFactors: [
          {
            uid: phoneFactorUid,
            phoneNumber: phoneNumber,
            displayName: 'Primary Phone',
            factorId: 'phone',
          },
        ],
      },
    });
    console.log(`Successfully created new auth user ${userRecord.uid} with email ${email} and enrolled MFA.`);
    return userRecord;
  } catch (error) {
    throw _handleAuthError(error, null, `create auth user ${email} with MFA`);
  }
}

/**
 * Revokes all refresh tokens for the given user. Refresh tokens are needed to refresh JWT tokens,
 * which have a lifespan of around 1 hour. Revoking a user session's refresh token effectively causes the user
 * session to expire within an hour.
 * See: https://firebase.google.com/docs/auth/admin/manage-sessions
 * @param {string} uid - The UID of the user.
 * @throws {https.HttpsError} If revoking tokens fails.
 */
async function _revokeUserSessions(uid) {
  try {
    await admin.auth().revokeRefreshTokens(uid);
    console.log(`Successfully revoked refresh tokens for user ${uid}.`);
  } catch (error) {
    throw _handleAuthError(error, uid, "revoke refresh tokens");
  }
}

// Helper function to generate a random alphanumeric password
function _generateRandomPassword() {
  const length = 12
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}
/**
 * Sets a temporary password for the user and a custom claim forcing password reset.
 * @param {string} uid - The UID of the user.
 * @returns {Promise&lt;string&gt;} The generated temporary password.
 * @throws {https.HttpsError} If updating user or claims fails.
 */
async function _setUserTemporaryPasswordAndClaim(uid) {
  const temporaryPassword = _generateRandomPassword();
  try {
    await admin.auth().updateUser(uid, { password: temporaryPassword });
    const userRecord = await admin.auth().getUser(uid); // Needed to get current claims
    const currentUserClaims = userRecord.customClaims || {};
    await admin.auth().setCustomUserClaims(uid, { ...currentUserClaims, mustResetPassword: true });
    console.log(`Set temporary password and mustResetPassword claim for user ${uid}.`);
    return temporaryPassword;
} catch (error) {
  throw _handleAuthError(error, uid, "set temporary password or claim");
} finally {
  // It's important to revoke sessions *after* all auth changes (incl. claims) are done.
  try {
    await _revokeUserSessions(uid); // This now uses _handleAuthError internally
    console.log(`Successfully revoked sessions for user ${uid} after password/claim update.`);
  } catch (revokeError) {
      // Log session revocation error but don't overshadow the primary error if one occurred.
      // The error from _revokeUserSessions (if any) is already an HttpsError.
      // If the main try block succeeded, this error will be the one thrown.
      console.error(`Error during session revocation for user ${uid} in _setUserTemporaryPasswordAndClaim's finally block:`, revokeError.message);
      if (!(error instanceof https.HttpsError)) { // If no primary error, throw this one
           throw revokeError;
      }
  } finally {
    // Always reflect users, regardless of revoke success/failure, if the primary operation might have changed data.
    await _reflectUsers();
  }
}
}
// --- EXPORTS ---

exports.createNewUser = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const { email, displayName, phoneNumber } = request.data;
  let createdUserRecordUid;
  console.log(`createNewUser called by admin ${request.auth.uid}. Data: email=${email}, displayName=${displayName}, phoneNumber=${phoneNumber}`);
  try {
    _validateAdminCreateUserInput(email, displayName, phoneNumber);
    console.log("Input validation successful for createNewUser.");

    const userRecord = await _createAuthUser(email, displayName, phoneNumber);
    createdUserRecordUid = userRecord.uid;
    console.log(`Auth user created and MFA enrolled: ${createdUserRecordUid}`);

    const temporaryPassword = await _setUserTemporaryPasswordAndClaim(createdUserRecordUid);

    return {
      success: true,
      message: "User created, MFA enrolled, temporary password set, and mustResetPassword claim added successfully. User must change password on next login.",
      uid: createdUserRecordUid,
      temporaryPassword: temporaryPassword,
    };
  } catch (error) {
    // Errors from helpers should already be HttpsError or handled by _handleAuthError
    if (error instanceof https.HttpsError) {
      throw error;
    }
    // Fallback for truly unexpected errors not converted by helpers
    console.error("Unexpected error in createNewUser:", error);
    throw new https.HttpsError("internal", "An unexpected error occurred in createNewUser.", error.message);
  } finally {
    // _reflectUsers is called within _setUserTemporaryPasswordAndClaim's finally block.
    // If _setUserTemporaryPasswordAndClaim itself fails before its finally, this ensures reflection.
    // If _createAuthUser fails, this ensures reflection (though less likely to be needed there).
    await _reflectUsers();
  }
}
);

exports.setUserActivity = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const callerUid = request.auth.uid;
  const targetUid = request.data.uid;
  const targetActive = request.data.active;

  _validateUid(targetUid);

  if (callerUid === targetUid) {
    throw new https.HttpsError(
      "failed-precondition",
      "One is not allowed to deactivate self using this function."
    );
  }
  
  const targetDisabled = !targetActive;
  try {
    console.info(`Admin ${callerUid} updating user ${targetUid} "disabled" to ${targetDisabled}.`);
    await admin.auth().updateUser(targetUid, { "disabled": targetDisabled });
    await _revokeUserSessions(targetUid); // Ensures user is logged out after status change
    await _reflectUsers(); // Reflect the change to Firestore
    return { message: `User account ${targetActive ? 'activated' : 'deactivated'} successfully.` };
  } catch (error) {
    throw _handleAuthError(error, targetUid, "update user activity");
  }
}
);

exports.adminChangeUserEmail = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const { uid, newEmail } = request.data;
  const result = await _adminUpdateUserProperty(request, uid, "email", newEmail,
    (val, name) => _validateString(val, name) // name will be "newEmail"
  );
  await _revokeUserSessions(uid);
  return result;
}
);

exports.adminChangeUserPhoneNumber = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const { uid, newPhoneNumber } = request.data;
  const result = await _adminUpdateUserProperty(request, uid, "phoneNumber", newPhoneNumber,
    (val, name) => _validatePhoneNumber(val, name) // name will be "newPhoneNumber"
  );
  await _revokeUserSessions(uid);
  return result;
}
);

exports.adminChangeUserDisplayName = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const { uid, newDisplayName } = request.data;
  const result = await _adminUpdateUserProperty(request, uid, "displayName", newDisplayName,
    (val, name) => { // name will be "newDisplayName"
      if (val !== null && typeof val !== 'string') {
        throw new https.HttpsError("invalid-argument", `New display name (${name}) must be a string or null.`);
      }
    }
  );
  await _revokeUserSessions(uid);
  return result;
}
);

exports.adminResetUserTemporaryPassword = https.onCall(
regionOpts,
async (request) => {
  await callerMustBeAdmin(request);
  const { uid } = request.data;

  _validateUid(uid);

  try {
    console.info(`Admin ${request.auth.uid} attempting to reset password for user ${uid}`);
    const temporaryPassword = await _setUserTemporaryPasswordAndClaim(uid);
    // _setUserTemporaryPasswordAndClaim calls _reflectUsers in its finally block.
    return { success: true, message: "User password reset successfully. User must change it on next login.", temporaryPassword: temporaryPassword };
  } catch (error) {
    // _setUserTemporaryPasswordAndClaim and its sub-functions use _handleAuthError or throw HttpsError
    if (error instanceof https.HttpsError) {
      throw error;
    }
    console.error(`Unexpected error in adminResetUserTemporaryPassword for user ${uid}:`, error);
    throw new https.HttpsError("internal", `Failed to reset user password for ${uid}.`, error.message);
  }
}
);

exports.userChangeOwnPassword = https.onCall(
regionOpts,
async (request) => {
  if (!request.auth || !request.auth.uid) {
    console.error("Unauthenticated call to userChangeOwnPassword.");
    throw new https.HttpsError("unauthenticated", "The function must be called while authenticated.");
  }
  const uid = request.auth.uid;
  const { newPassword } = request.data;

  _validateString(newPassword, "newPassword");

  try {
    console.log(`User ${uid} attempting to change their own password.`);
    await admin.auth().updateUser(uid, { password: newPassword });
    console.log(`Password updated successfully for user ${uid}.`);

    const userRecord = await admin.auth().getUser(uid);
    const currentUserClaims = userRecord.customClaims || {};
    await admin.auth().setCustomUserClaims(uid, { ...currentUserClaims, mustResetPassword: false });
    console.log(`Custom claim 'mustResetPassword' set to false for user ${uid}.`);
    
    await _revokeUserSessions(uid); // Revoke sessions after password and claim change
    
    await _reflectUsers(); // Reflect all changes to Firestore
    console.log(`User data reflected to Firestore for user ${uid} after password change.`);

    return { success: true, message: "Password changed successfully." };
  } catch (error) {
    throw _handleAuthError(error, uid, "user change own password");
  }
}
);