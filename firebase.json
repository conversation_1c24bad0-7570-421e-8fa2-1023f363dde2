{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"auth": {"port": 9099, "host": "0.0.0.0"}, "functions": {"port": 5001, "host": "0.0.0.0"}, "firestore": {"port": 8080, "host": "0.0.0.0"}, "hosting": {"port": 5000, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 8088, "host": "0.0.0.0"}, "singleProjectMode": true}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}]}