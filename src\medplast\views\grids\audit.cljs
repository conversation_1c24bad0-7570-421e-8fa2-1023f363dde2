(ns medplast.views.grids.audit
  (:require ; [debux.cs.core :refer-macros [clog clogn dbg dbgn break
 ;                                     clog_ clogn_ dbg_ dbgn_ break_]]
   [cljs-bean.core :refer [->js]] ; [debux.cs.core :refer-macros [clog clogn dbg dbgn break
   [clojure.string]
   [jiff]
   ["json-diff-kit" :refer [Differ Viewer]] ; ["json-diff-kit/dist/viewer.css"]
   [medplast.state :as state]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe] ;[cljc.java-time.period :as time-period]
   ))

(def differ (new Differ))

(defn- changes-cell-renderer-component [docBefore-js docAfter-js]
  ; https://github.com/cujojs/jiff
  (let [diff (.diff differ (->js docBefore-js) docAfter-js)]
    [:div {:class ""}
     ; https://github.com/RexSkz/json-diff-kit
     [:> Viewer {:diff diff :highlightInlineDiff true}]]))

(defn- make-changes-cell-renderer []
  (fn [params]
    (let [{:keys [patchString docBefore] :as _log} (grids-core/get-row-data params)]
      (when patchString
        (let [patch-js (js/JSON.parse patchString)
              docBefore-js (->js docBefore)
              docAfter-js (jiff/patch patch-js docBefore-js)]
          (r/as-element
           [changes-cell-renderer-component docBefore-js docAfter-js]))))))

(defn- document-link-renderer [params]
  ;(js/console.log params)
  (let [document-type (grids-core/get-cell-data params)
        row (grids-core/get-row-data params)
        document-link (:document-link row)]
    (r/as-element (views-core/href-link document-link document-type))))

(def col-defs
  [{:field "timestamp" :headerName "Laikas (UTC)" :width 200 :filter true}
   {:field "author"
    :headerName "Pakeitimo autorius"
    :cellRenderer grids-core/user-cell-renderer
    :valueFormatter grids-core/user-value-formatter
    :wrapText true
    :filterParams
    #js {:filteroptions #js ["contains"]
         :textFormatter grids-core/user-text-formatter}
    :filter true}
   {:field "owner" :headerName "Dokum. savininkas"
    :cellRenderer grids-core/user-cell-renderer
    :valueFormatter grids-core/user-value-formatter
    :filterParams
    #js {:filteroptions #js ["contains"]
         :textFormatter grids-core/user-text-formatter}
    :filter true}
   {:field "document-type" :headerName "Dokum."
    :width 150
    :cellRenderer document-link-renderer
    :filterParams #js {:filteroptions #js ["contains"]}
    :filter true}
   {:field "patient" :headerName "Pacientas"
    :cellRenderer grids-core/patient-cell-renderer
    :valueFormatter grids-core/patient-value-formatter
    :filterParams
    #js {:filteroptions #js ["contains"]
         :textFormatter grids-core/patient-text-formatter}
    :filter true}
   ; {:valueGetter "data.docPathParams.pathUserId" :headerName "pathUserId" :width 250}
   ; {:valueGetter "data.docPathParams.pathUserCol" :headerName "pathUserCol" :width 250}
   ; {:valueGetter "data.docPathParams.pathDocId" :headerName "pathDocId" :width 250}
   {:headerName "Prieš / po"
    :cellRenderer (make-changes-cell-renderer)
    :flex 1
    :minWidth 800
    :autoHeight true}])

;; User information functions
;; Helper functions for document type handling
(defn- get-document-collection-and-id
  "Extract document collection and ID from a document path or parameters.
   Returns a map with :collection and :id keys."
  [row]
  (let [user-col (-> row :docPathParams :pathUserCol)
        doc-path (:docPath row)]
    (if (nil? user-col)
      ;; Root-level document (e.g., product)
      (let [path-parts (clojure.string/split doc-path #"/")]
        {:collection (first path-parts)
         :id (second path-parts)
         :user-relative? false})
      ;; User-relative document (e.g., sale, patient)
      {:collection user-col
       :id (-> row :docPathParams :pathDocId)
       :user-id (-> row :docPathParams :pathUserId)
       :user-relative? true})))

;; User information functions
(defn- assoc-author-or-author-id
  "Associate author information with a row.

   Uses the authId from the row to look up the user in the id-to-user-map.
   If the user is found, associates the user object with the row.
   Otherwise, associates the authId as a fallback."
  [id-to-user-map row]
  (let [author-id (:authId row)
        author (get id-to-user-map author-id)
        author (or author author-id)]
    (assoc row :author author)))

(defn- assoc-owner
  "Associate document owner information with a row.

   For user-relative documents:
   - Uses the pathUserId from the row to look up the user in the id-to-user-map.
   - If the user is found, associates the user object with the row.
   - Otherwise, associates the user ID as a fallback.

   For non-user-relative documents (like product):
   - Associates a special '-' marker to indicate N/A."
  [id-to-user-map row]
  (let [{:keys [user-relative?]} (get-document-collection-and-id row)]
    (if user-relative?
      ;; For user-relative documents, look up the owner
      (let [owner-id (-> row :docPathParams :pathUserId)
            owner-user (get id-to-user-map owner-id)
            value (or owner-user owner-id)]
        (assoc row :owner value))
      ;; For non-user-relative documents, use a special marker
      (assoc row :owner "-"))))

;; Helper functions for document type handling

(defn- get-document-display-name
  "Get a human-readable display name for a document collection."
  [collection]
  (case collection
    "sale" "Pardavimas"
    "patient" "Pacientas"
    "product" "Produktas"
    (str "Dokumentas: " collection)))

(defn- get-route-id-for-document
  "Get the route ID for a document based on its collection."
  [collection]
  (case collection
    "sale" :medplast.routes/sale-view
    "patient" :medplast.routes/patient-view
    "product" :medplast.routes/product-view
    nil))

(defn- create-document-link
  "Create a link to a document based on its collection, ID, and user ID (if applicable)."
  [{:keys [collection id user-id user-relative?]}]
  (let [route-id (get-route-id-for-document collection)]
    (when route-id
      (if user-relative?
        (let [has-required-ids? (and user-id id)]
          (when has-required-ids?
            (rfe/href route-id {:user user-id :id id})))
        (when id
          (rfe/href route-id {:id id}))))))

;; Main document processing functions
(defn- assoc-document-type
  "Associate a document type display name with a row."
  [row]
  (let [{:keys [collection]} (get-document-collection-and-id row)
        display-name (get-document-display-name collection)]
    (assoc row :document-type display-name)))

(defn- assoc-document-link
  "Associate a document link with a row."
  [row]
  (let [doc-info (get-document-collection-and-id row)
        link (create-document-link doc-info)]
    (if link
      (do
        ;(println "Created document link:" link)
        (assoc row :document-link link))
      (do
        ;(println "Could not create link for document:" doc-info)
        row))))

(defn- extract-patient-id-from-patch-string
  "Extract patient ID from a patch string using regex."
  [patch-string]
  (when patch-string
    (let [regex #".*\"patient\":\"([^\"]+)\".*"
          matches (re-find regex patch-string)]
      (second matches))))

(defn- get-patient-id-for-document
  "Get patient ID for a document based on its collection and data."
  [row collection]
  (try
    (case collection
      "sale" (-> row :docBefore :patient)
      "patient" (-> row :docPathParams :pathDocId)
      nil)
    (catch :default e
      (println "Error getting patient-id:" e)
      nil)))

(defn- assoc-patient
  "Associate patient information with a row.

   For patient-related documents (sale, patient):
   - Extracts the patient ID from the document
   - Looks up the patient in the patient map
   - Associates the patient object or ID with the row

   For non-patient-related documents (like product):
   - Associates a special '-' marker to indicate N/A"
  [id-to-patient-map row]
  (let [{:keys [collection user-relative?]} (get-document-collection-and-id row)
        ;; Extract the condition into a named binding for clarity
        is-patient-related? (and user-relative? (#{"sale" "patient"} collection))]
    ;; Only process patient data for sale and patient documents
    (if is-patient-related?
      (let [patient-id (get-patient-id-for-document row collection)
            ;; Extract another complex condition into a binding
            is-new-sale? (and (nil? patient-id) (= collection "sale"))
            ;; Handle sale creation case where patient ID is in the patch
            patient-id (if is-new-sale?
                         (try
                           (extract-patient-id-from-patch-string (:patchString row))
                           (catch :default e
                             (println "Error extracting patient-id from patch:" e)
                             nil))
                         patient-id)
            ;; Look up patient in the map
            patient (when patient-id (get id-to-patient-map patient-id))
            value (or patient patient-id)]
        (if value
          (assoc row :patient value)
          row))
      ;; For non-patient-related documents, use a special marker
      (assoc row :patient "-"))))

(defn- process-row-data
  "Process each row of audit log data to add user, document, and patient information.

   This function applies a series of transformations to each row:
   1. Adds author information
   2. Adds document owner information
   3. Adds document type display name
   4. Adds document link
   5. Adds patient information (for relevant document types)"
  [id-to-user-map id-to-patient-map row-data]
  (->>
   row-data
   (map (partial assoc-author-or-author-id id-to-user-map))
   (map (partial assoc-owner id-to-user-map))
   (map assoc-document-type)
   (map assoc-document-link)
   (map (partial assoc-patient id-to-patient-map))))

(defn- changes-table
  "Render a table of audit log entries.

   This component:
   1. Fetches raw log data from the state
   2. Fetches user and patient maps for lookups
   3. Processes each row to add display information
   4. Renders the table with the processed data"
  []
  (let [;; Get the raw log data
        raw-log-data @(r/track state/get-log-subscription-atom)

        ;; Get lookup maps for users and patients
        users-map @(r/track state/get-id-to-user-map-atom)
        patients-map @(r/track state/get-id-to-patient-map-atom)

        ;; Create a reactive atom that processes the raw data
        processed-data (r/reaction
                        ;; Simply return the processed data without the redundant do
                        (process-row-data @users-map
                                         @patients-map
                                         @raw-log-data))]

    ;; Render the table with the processed data
    [table {:row-data-atom processed-data
            :col-defs col-defs}]))

(defn audit-page
  "Main audit log page component.

   Renders a header with navigation controls and the audit log table."
  []
  (r/with-let []
    [:<>
     [header "Pakeitimai"
      {:on-click views-core/go-back! :text "Atgal"}]
     [changes-table]]))
