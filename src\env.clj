(ns env
  (:refer-clojure :exclude [get])
  (:require [shadow-env.core :as shadow-env]))

;; This function will read environment variables
(defn- read-env [_build-state]
  (let [emulator-host (or (System/getenv "FIREBASE_EMULATOR_HOST") "localhost")
        auth-port (or (System/getenv "FIREBASE_AUTH_EMULATOR_PORT") "9099")
        functions-port (or (System/getenv "FIREBASE_FUNCTIONS_EMULATOR_PORT") "5001")
        firestore-port (or (System/getenv "FIREBASE_FIRESTORE_EMULATOR_PORT") "8080")]
    {:common {:firebase-emulator {:host emulator-host
                                  :auth-port auth-port
                                  :functions-port functions-port
                                  :firestore-port firestore-port}}
     :clj {}
     :cljs {}}))

;; Link the environment reader function
#_{:clj-kondo/ignore [:unresolved-symbol]}
(shadow-env/link getter `read-env)

(defmacro get [k]
  #_{:clj-kondo/ignore [:unresolved-symbol]}
  (if (bound? #'getter)
    (clojure.core/get getter k)
    (throw (ex-info "env/getter not bound - make sure shadow-env.core/link has been called" {:k k}))))