## High-Level Architecture (Inferred)

The system appears to follow a client-server architecture, specifically:
*   **Frontend:** A Single Page Application (SPA) built with ClojureScript, running in the user's browser.
*   **Backend:** Leveraging Firebase services (Backend-as-a-Service - BaaS), including:
    *   Firestore as the primary database.
    *   Firebase Functions for server-side logic (likely Node.js based on `functions/index.js` and `functions/package.json`).
    *   Firebase Hosting for serving the SPA.
    *   Firebase Authentication for user management.

## Key Components (Detected)

*   **ClojureScript Frontend (`src/medplast/`)**:
    *   Core application logic (`core.cljs`).
    *   UI Views/Components (`views/core.cljs`, `views/pages.cljs`, `views/avatar.cljs`).
    *   Specialized Form components (`views/forms/`).
    *   Specialized Data Grid components (`views/grids/`).
        *   Note: The `core.cljs/form` component in `views/forms/` automatically includes a submit button unless the `read-only` flag is set.
    *   State management (`state.cljs`, likely using Re-frame or similar).
    *   Routing (`routes.cljs`).
    *   Firebase interaction layer (`firebase.cljs`, `firestore.cljs`).
*   **Firebase Functions Backend (`functions/`)**: Server-side logic triggered by HTTP requests or Firebase events.
*   **Firebase Services**:
    *   Firestore Database (schema inferred from `firestore.rules`, `firestore.indexes.json`, and frontend usage).
    *   Firebase Authentication.
    *   Firebase Hosting.
*   **Build System**: `shadow-cljs` for ClojureScript compilation, `npm` for JavaScript dependency management (frontend and backend).
*   **Styling**: Tailwind CSS (`tailwind.config.js`, `css/app.css`).

## Design Patterns (Observed)

*   **Single Page Application (SPA)**: Frontend architecture.
*   **Component-Based UI**: Implied by the `views/` structure and likely use of a ClojureScript UI library (e.g., Reagent/Re-frame).
*   **Backend-as-a-Service (BaaS)**: Heavy reliance on Firebase for backend infrastructure.
*   **Model-View-Controller/Model-View-ViewModel (MVC/MVVM) variant**: Likely separation of concerns between data (`state.cljs`, Firestore interaction), UI (`views/`), and potentially controller logic within components or core files. Re-frame (if used) implements its own event-driven pattern.
*   **Configuration Management**: Separate environment files (`env.clj`, `env.cljc`, `env.cljs`).
### State Management and Persistence Patterns

Based on the analysis of `src/medplast/state.cljs`, `src/medplast/firebase.cljs`, `src/medplast/firestore.cljs`, `src/medplast/views/grids/sale.cljs`, and `src/medplast/views/forms/sale.cljs`, the following system patterns are evident:

- **Reactive State Management (Reagent Atoms):** The application heavily utilizes Reagent atoms (`reagent.core/atom`, `reagent.core/reaction`, `reagent.core/track`) for managing application state. This allows for a reactive UI where components automatically re-render when the underlying state changes.
- **Firestore Real-time Subscriptions (`onSnapshot`):** `medplast.firestore` provides functions (`subscribe-to-docs`, `subscribe-to-doc`) that wrap the Firestore JS SDK's `onSnapshot` functionality. These are used by `medplast.state` to create reactive atoms that stay synchronized with the Firestore database in real-time. This is a core pattern for ensuring the UI reflects the latest data.
- **Asynchronous Operations with Core.async Channels:** Asynchronous operations, particularly interactions with Firebase and Firestore APIs (which return JavaScript Promises), are handled using `cljs.core.async` channels and the `<?js-promise-to-chan` helper. This provides a sequential and manageable way to deal with asynchronous workflows within ClojureScript's synchronous execution model. The `go-try` macro is used to simplify error handling in these asynchronous blocks.
- **Protocol-Oriented Firestore API:** `medplast.firestore` defines protocols (`ColLookupProtocol`, `ColUpdateProtocol`, `DocLookupProtocol`, `ColQueryableProtocol`) and records (`ColRef`, `ColGroupRef`, `DocRef`) to provide a more idiomatic and structured ClojureScript interface to the Firestore JS SDK. This abstracts away some of the underlying JavaScript details and provides a clearer way to represent and interact with Firestore entities.
- **Separation of Concerns:** There is a clear separation between the state management layer (`medplast.state`), the low-level persistence interaction layers (`medplast.firebase`, `medplast.firestore`), and the view layer (`medplast.views.*`). `medplast.state` acts as a mediator, providing data to the views and coordinating data changes with the persistence layers.
- **Data Transformation in Views:** View components often perform data transformation or enrichment (e.g., associating related data like salesperson or patient details) on the raw data received from the state atoms before rendering it. This keeps the state atoms focused on the core data while allowing views to prepare data for presentation.
- **Route-Driven Data Context:** The application state, particularly in `medplast.state`, is influenced by the current route parameters (e.g., user ID or document ID from the URL). Functions like `get-implied-user-uid-atom` demonstrate how the route context determines which data is being tracked and displayed.
- **Firebase Cloud Functions for Backend Logic:** Firebase Cloud Functions are used for backend operations that require server-side execution, such as updating user activity or moving patient data. `medplast.firebase` provides functions to call these callable functions asynchronously.
- **Reagent Lifecycle Integration for Subscriptions:** The `register-for-unsubscription` helper in `medplast.firestore` is used to tie Firestore subscriptions to the lifecycle of Reagent components, ensuring that subscriptions are properly unsubscribed when components are unmounted to prevent memory leaks.