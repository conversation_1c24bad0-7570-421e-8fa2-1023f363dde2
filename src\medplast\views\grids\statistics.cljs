(ns medplast.views.grids.statistics
  (:require
   [cljc.java-time.local-date :as local-date]
   [clojure.string]
   [medplast.firestore :as firestore]
   [medplast.lang :as lang :refer [parse-local-date]]
   [medplast.state :as state]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.forms.core :as forms-core]
   [medplast.views.forms.sale :as forms-sale]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [reagent.core :as r]))

(defn valid-sale? [sale]
  (let [stock-expiration-date (parse-local-date (:stock-expiration-date sale))
        sale-date (parse-local-date (:sale-date sale))
        valid-sale (> stock-expiration-date sale-date)]
    valid-sale))

(defn project-sale [sale]
  ; given a sale, return another sale that has same duration and starts when
  ; the first one ended
  (let [stock-expiration-date (parse-local-date (:stock-expiration-date sale))
        projected-sale (forms-sale/get-initial-replacement-sale-from-old-sale
                        sale {:new-sale-date stock-expiration-date})]
    projected-sale))

(defn get-product-id-to-quantity-map-from-sales [sales]
  ; flattens the sales sequence, so [[1],[1]] == [1,1]
  (let [reduce-sales-to-stock-requirements
        (fn [stocks sale]
          (let [stock-with-ids-dekeywordized (-> sale :stock (update-keys name))]
            (merge-with + stocks stock-with-ids-dekeywordized)))
        product-id-to-quantity-requirements
        (->>
         sales
         flatten
         (reduce reduce-sales-to-stock-requirements {}))]
    product-id-to-quantity-requirements))

; NOTE, inf-sales means the sequence might be infinite, don't materialize
(defn slice-sale-period-from-chronological-sales [period inf-sales]
  ; slices out sales that were performed during the period
  ; sales have to be in chronological order
  (let [sale-sold-before-start-of-period
        (fn [sale]
          (< (parse-local-date (:sale-date sale))
             (:start-date period)))
        sale-sold-before-end-of-period
        (fn [sale]
          (<= (parse-local-date (:sale-date sale))
              (:end-date period)))]
    (->>
     inf-sales
     ; drop sales while expiration date (not sale date) before period start
     (drop-while sale-sold-before-start-of-period)
     ; take sales while sale date before-or-at period end
     (take-while sale-sold-before-end-of-period))))

; NOTE, inf-sales means the sequence might be infinite, don't materialize
#_(defn slice-expiration-period-from-chronological-sales [period inf-sales]
  ; slices out sales that expire during the period
  ; sales have to be in chronological order
    (let [sale-expires-before-start-of-period
          (fn [sale]
            (< (parse-local-date (:stock-expiration-date sale))
               (:start-date period)))
          sale-expires-before-end-of-period
          (fn [sale]
            (<= (parse-local-date (:stock-expiration-date sale))
                (:end-date period)))]
      (->>
       inf-sales
     ; drop sales while expiration date (not sale date) before period start
       (drop-while sale-expires-before-start-of-period)
     ; take sales while expiration date before-or-at period end
       (take-while sale-expires-before-end-of-period))))

(defn get-projected-sales-atom [period user-uid]
  (let [active-sales-atom
        (r/reaction
         (if user-uid
           @@(r/track state/get-user-active-sales-atom user-uid)
           @@(r/track state/get-all-active-sales-atom)))
        projected-sales-atom
        (r/reaction
         (when-let [active-sales @active-sales-atom]
           (->>
            active-sales
            ; filter out invalid sales
            (filter valid-sale?)
            ; perform projection for each sale
            (map #(iterate project-sale %))
            ; remove first sale for each sale chain.
            ; that's the non-fictive (currently active) sale
            (map rest)
            (map (partial slice-sale-period-from-chronological-sales period))
            flatten)))]
    projected-sales-atom))

(defn get-past-sales-atom [period user-uid]
  (let [sales-expiration-ordered-atom
        (r/reaction
         (if user-uid
           @@(r/track state/get-user-valid-sold-sales-expiration-ordered-atom user-uid)
           @@(r/track state/get-all-valid-sales-expiration-ordered-atom)))
        sales-in-period-atom
        (r/reaction
         (slice-sale-period-from-chronological-sales
          period @sales-expiration-ordered-atom))]
    sales-in-period-atom))

(def col-defs
  [{:field "type" :headerName "Rūšis" :width 150
    :autoHeight true
    :wrapText true}
   {:field "name" :headerName "Pavadinimas"
    :flex 0.15 :minWidth 150
    :maxWidth 250
    :autoHeight true :wrapText true}
   {:field "variant-name" :headerName "Varianto pavad."
    :flex 0.3 :minWidth 250
    :maxWidth 350
    :autoHeight true
    :wrapText true
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "quantity-sold" :headerName "Kiekis parduotas"
    :width 120
    :filter true
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "quantity-projected" :headerName "Kiekis kurį tikimasi parduoti"
    :width 120
    :filter true
    :autoHeaderHeight true
    :wrapHeaderText true}])

(def initial-column-config
  [{:colId "type", :sort "asc", :sortIndex 0}
   {:colId "name", :sort "asc", :sortIndex 1}
   {:colId "variant-name", :sort "asc", :sortIndex 2}])

(defn sales-stock-requirements-table [period-atom user-uid]
  (let [get-product-id-to-keyed-quantity-map
        (fn [key sales]
          (when sales
            (->
             (get-product-id-to-quantity-map-from-sales sales)
             (update-vals (fn [quantity] (assoc {} key quantity))))))
        product-id-to-projected-quantity-column-atom
        (r/reaction
         (get-product-id-to-keyed-quantity-map
          :quantity-projected
          @@(r/track get-projected-sales-atom @period-atom user-uid)))
        product-id-to-past-quantity-column-atom
        (r/reaction
         (get-product-id-to-keyed-quantity-map
          :quantity-sold
          @@(r/track get-past-sales-atom @period-atom user-uid)))
        product-id-to-columns-atom
        (r/reaction
         (when-let [a @product-id-to-projected-quantity-column-atom]
           (when-let [b @product-id-to-past-quantity-column-atom]
             (merge-with merge a b))))
        row-data-atom
        (r/reaction
         (when-let [product-id-to-columns @product-id-to-columns-atom]
           (when-let [id-to-product-map @@(r/track state/get-id-to-product-map-atom)]
             (->>
              (update-keys
               product-id-to-columns
               id-to-product-map)
              (map
               (fn [[{:keys [name variant-name type] :as product}
                     {:keys [quantity-projected quantity-sold] :as _column}]]
                 {:name name
                  :variant-name variant-name
                  :type type
                  :quantity-projected quantity-projected
                  :quantity-sold quantity-sold
                  :id (firestore/get-doc-id product)}))))))]
    [table {:row-data-atom row-data-atom
            :col-defs col-defs
            :initial-column-config initial-column-config}]))

(defn period-from-match [match]
  (let [{:keys [start-date end-date]} (:query-params match)]
    (when (and start-date end-date)
      (forms-core/make-period (parse-local-date start-date) (parse-local-date end-date)))))

(defn statistics-page [match]
  (r/with-let [initial-period
               (or (period-from-match match)
                   (forms-core/Period. (local-date/now) (local-date/now)))
               period-atom (r/atom initial-period)
               _
               (add-watch
                period-atom :period-watch
                (fn [_ _ _old-period new-period]
                  (state/replace-query-params!
                   {:start-date (str (:start-date new-period))
                    :end-date (str (:end-date new-period))})))]
    (let [active-users-atom @(r/track state/get-active-users-atom)]
      [:<>
       [header "Laikotarpio pasirinkimas"
        {:on-click views-core/go-back! :text "Atgal"}]
       [forms-core/period-selector period-atom]
       (if (forms-core/valid-period? @period-atom)
         [:<>
          [header "Visi vartotojai"]
          [sales-stock-requirements-table period-atom nil]
          ; [patient-activation-per-user period-atom]
          [header "Aktyvūs vartotojai atskirai"]
          (if-let [active-users @active-users-atom]
            (for [user active-users]
              (let [user-uid (firestore/get-doc-id user)]
                ^{:key user-uid} [:<>
                                  [:div
                                   {:class "-mb-3"}
                                   [views-core/href-link
                                    (views-core/get-href-for-user user)
                                    (views-core/rich-user-label user)]]
                                  [sales-stock-requirements-table period-atom user-uid]]))
            "Kraunama...")]
         [:span "Pradžios data turi būti anksčiau negu (arba ta pati kaip) pabaigos data."])])
    (finally
      (remove-watch period-atom :period-watch))))
