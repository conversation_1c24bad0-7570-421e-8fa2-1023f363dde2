(ns medplast.connectivity
  (:require
   [reagent.core :as r]
   [cljs.core.async :refer [go <!]]
   [medplast.lang :refer-macros [<?] :refer [go-try]]))

(defonce ^:private connectivity-state-atom (r/atom {:online true :initialized false}))

(defn get-connectivity-atom
  "Returns a Reagent atom that tracks the current network connectivity state.
  The atom contains a map with :online (boolean) and :initialized (boolean) keys.
  :online indicates if the browser is currently online
  :initialized indicates if the connectivity detection has been set up"
  []
  connectivity-state-atom)

(defn online?
  "Returns true if the browser is currently online, false otherwise.
  Uses the current state from the connectivity atom."
  []
  (:online @connectivity-state-atom))

(defn offline?
  "Returns true if the browser is currently offline, false otherwise.
  Uses the current state from the connectivity atom."
  []
  (not (online?)))

(defn- update-connectivity-state!
  "Updates the connectivity state atom with the current navigator.onLine value."
  []
  (let [online (.-onLine js/navigator)]
    (swap! connectivity-state-atom assoc :online online)
    (println "Connectivity changed:" (if online "online" "offline"))))

(defn- setup-connectivity-listeners!
  "Sets up event listeners for online/offline events and initializes the connectivity state."
  []
  (when-not (:initialized @connectivity-state-atom)
    ; Set initial state
    (update-connectivity-state!)

    ; Add event listeners
    (.addEventListener js/window "online" update-connectivity-state!)
    (.addEventListener js/window "offline" update-connectivity-state!)

    ; Mark as initialized
    (swap! connectivity-state-atom assoc :initialized true)
    (println "Connectivity monitoring initialized")))

(defn init-connectivity-monitoring!
  "Initializes network connectivity monitoring.
  Should be called once during application startup."
  []
  (setup-connectivity-listeners!))

(defn is-network-error?
  "Checks if an error is likely a network connectivity error.
  Returns true for common network error patterns."
  [error]
  (when (instance? js/Error error)
    (let [message (.-message error)
          code (.-code error)]
      (or
       ; Firebase function errors
       (= code "functions/unavailable")
       (= code "functions/deadline-exceeded")
       (= code "functions/internal")
       ; Generic network errors
       (and message
            (or (.includes message "network")
                (.includes message "fetch")
                (.includes message "connection")
                (.includes message "timeout")
                (.includes message "offline")))))))

(defn should-retry-on-error?
  "Determines if an operation should be retried based on the error type.
  Returns true for network errors when the device comes back online."
  [error]
  (and (is-network-error? error) (online?)))
