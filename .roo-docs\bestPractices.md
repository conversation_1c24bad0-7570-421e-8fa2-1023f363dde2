## Best Practices
### Medplast Form and Input Framework Documentation and Best Practices

**Core Components and Functionality:**

*   **State Management:** The forms heavily rely on Reagent atoms (managed by `state-atom` and `state-cursor`) to manage form data. Changes in input fields directly update these atoms.
*   **Input Handling:** The `make-input-handler` function provides a standardized way to handle input changes, parsing values based on expected types (`:deserialize`, `:int`, or default string).
*   **Input Types:** The framework supports various input types, including `:str`, `:int`, `:date`, `:datetime-local`, `:doc-ref`, `:long-str` (using `textarea`), and `:select`.
*   **Select Component:** The `select` component is used for dropdowns, handling serialization/deserialization of values and managing a "prompt" option for required fields. It also supports linking to other views based on the selected value when in read-only mode.
*   **Textarea Component:** A custom `textarea` component is provided, which appears to handle automatic resizing based on content.
*   **Input Component:** The `input` component is a versatile component for various input types, integrating with state management, handling read-only states, and supporting datalists.
*   **Checkbox Component:** A simple `checkbox` component is implemented using the `select` component with "Taip" and "Ne" options.
*   **Labeled Components:** `labeled` and `labeled-input` are utility components for wrapping form elements with labels and handling conditional hiding based on a `hide-set`.
*   **Specific Select Components:** `user-select` and `patient-select` are specialized select components for choosing users and patients, integrating with Firestore subscriptions to fetch data.
*   **Form Submission:**
    *   `submit-button` provides a styled and disabled-aware submit button.
    *   `form-container` provides a basic container for form elements.
    *   `make-check-and-submit-callback` creates a handler that prevents default submission, checks form validity, and calls either a `submit-fn` or `invalid-fn`.
    *   `form-submit-button` integrates the `submit-button` with the validity checking callback.
    *   `maybe-error-notification` displays error messages from an `error-state` atom.
    *   `error-and-submit-controls` combines error display and the submit button, conditionally rendering based on `read-only` state.
    *   `form` is the main form component, managing submission state (`submit-pending-atom`), error state (`error-state`), and integrating validity checking and submission logic. It uses core.async for handling asynchronous submission.
    *   **Period Selection:** `PeriodProtocol`, `Period` record, `make-period`, and `period-selector` provide components and logic for selecting date ranges.
*   **Validity Constraints:** `watch-inactive-validity-constraint` and `watch-inactive-constraint` are functions for adding watchers to the state atom to enforce validity rules (e.g., marking a document inactive if invalid or if certain boolean flags are true).
*   **Clarification:** A simple `clarification` component for displaying explanatory text.
*   **Asynchronous Submission Handling:** `<?handle-valid-submit!` is a function using core.async (`go-try`, `<!`, `<?`) to handle asynchronous submission attempts and success callbacks.

**Derived Best Practices:**

1.  **Centralized State Management:** Utilize Reagent atoms (`state-atom`, `state-cursor`) for managing form data, ensuring a single source of truth.
2.  **Standardized Input Handling:** Employ `make-input-handler` or similar patterns for consistent handling and parsing of input values across different input types.
3.  **Component Reusability:** Leverage the provided core components (`input`, `select`, `labeled-input`, etc.) to maintain consistency in form appearance and behavior.
4.  **Clear Separation of Concerns:** The `form` component should handle the overall submission logic, validation triggering, and error display, while individual input components focus on managing their specific input state and appearance.
5.  **Asynchronous Operations:** Use core.async (`go`, `<!`, `<?`) for handling asynchronous operations like form submission to avoid blocking the UI.
6.  **Read-Only State:** Properly utilize the `read-only` flag to disable inputs and modify component behavior as needed.
7.  **Validity Feedback:** Provide clear visual feedback to the user regarding input validity and form errors using components like `maybe-error-notification`.
8.  **Specialized Components:** Create specialized components (like `user-select`, `patient-select`) for complex input types or data dependencies, encapsulating their specific logic.
9.  **Constraint Watching:** Implement watchers (`watch-inactive-validity-constraint`, `watch-inactive-constraint`) for enforcing data constraints and business rules based on state changes.
10. **Minimize Javascript Interop in High-Level Code:** Javascript interop (`.-property`, `js/`) should be confined to the lowest-level functions that interact directly with Javascript libraries or APIs. Data structures passed between Javascript and Clojurescript should be explicitly converted to Clojurescript maps, vectors, etc., using functions like `->clj` as soon as they enter the Clojurescript domain. Avoid accessing Javascript properties or calling Javascript methods directly in high-level application logic or UI components.