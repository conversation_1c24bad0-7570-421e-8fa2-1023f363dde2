(ns medplast.views.grids.user
  (:require
      ;[reagent.debug :as debug]
   #_["firebase/firestore" :as fs]
   [clojure.string]
   #_[medplast.lang :as lang]
   [medplast.state :as state]
   [medplast.firestore :as firestore]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]
   #_[cljc.java-time.local-date :as local-date]
   ;[cljc.java-time.period :as time-period]
   #_[cljc.java-time.temporal.chrono-unit :as chrono-unit]))

(def action-cell-renderer
  (grids-core/make-action-cell-renderer
   nil
   (fn [doc]
     (let [doc-id (firestore/get-doc-id doc)]
       [{:href (rfe/href :medplast.routes/user-view {:id doc-id})
         :text "Per<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
        {:href (rfe/href :medplast.routes/user-edit {:id doc-id})
         :text "Redaguoti"}]))))

(def col-defs
  [{:field "actions"
    :headerName "Veiksmai"
    :cellRenderer action-cell-renderer
    :width 180
    :sortable false
    :autoHeight true
    :wrapText true}
   {:field "admin" :headerName "Administratorius?" :width 150
    :cellRenderer grids-core/boolean-cell-renderer}
   {:field "display-name" :headerName "Priskirtas vardas"
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}
    :minWidth 300}
   {:field "email" :headerName "El. paštas"
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}
    :minWidth 300}
   {:field "creation-time" :headerName "Sukūrimo data"
    :width 240}
   {:field "last-sign-in-time" :headerName "Paskutinė įsiregistravimo data"
    :width 240}
   {:field "active" :headerName "Aktyvus?" :width 150
    :cellRenderer grids-core/boolean-cell-renderer}])

(def initial-column-config
  [{:colId "active", :sort "desc", :sortIndex 0}
   {:colId "creation-time", :sort "desc", :sortIndex 1}])

(defn users-table []
  (r/with-let [users-atom @(r/track state/get-users-atom)
               users-atom
               (r/reaction (map views-core/process-user @users-atom))]
    [table {:row-data-atom users-atom
            :col-defs col-defs
            :initial-column-config initial-column-config}]))

(defn user-list-page []
  [:<>
   [header "Vartotojai"
    {:on-click views-core/go-back! :text "Atgal"}
    {:href (rfe/href :medplast.routes/user-register)
     :admin-only true
     :text "Naujo vart. registracija"}]
   [users-table]])
