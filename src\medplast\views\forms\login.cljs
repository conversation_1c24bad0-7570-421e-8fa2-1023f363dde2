(ns medplast.views.forms.login
  (:require
   [medplast.views.core :as views-core]
   [medplast.views.forms.core :refer [form labeled-input]]
   [medplast.firebase :as fb]
   [medplast.cloud-fn :as cloud-fn]
   [reagent.core :as r]
   [cljs-bean.core :refer [->clj]]
   [medplast.lang :refer-macros [go-try <?]]
   [cljs.core.async :refer [go <!]]
   [reitit.frontend.easy :as rfe]
   ["firebase/auth" :as fb-auth]))

(def recaptcha-container-id "login-recaptcha-container")

;; --- State Mutation Functions ---

(defn- unset-recaptcha-verifier! [state-atom]
  (swap! state-atom dissoc :recaptcha-verifier))

(defn- set-recaptcha-verifier! [state-atom verifier]
  (swap! state-atom assoc :recaptcha-verifier verifier))

(defn- handle-mfa-verification-sent! [state-atom verification-id]
  (swap! state-atom assoc
         :step :enter-mfa-code
         :verification-id verification-id
         :loading? false))

(defn- set-mfa-resolver-and-hint! [state-atom resolver hint]
  (swap! state-atom assoc :resolver resolver :hint hint))

;; --- Error Handlers ---

(defn- invalid-code-error? [error]
  (= (.-code error) "auth/invalid-verification-code"))

;; --- Success Handler ---

(defn- handle-login-success! []
  (rfe/navigate :medplast.routes/frontpage))

;; --- MFA & Recaptcha Setup ---

(defn- init-recaptcha
  "Initializes the Firebase reCAPTCHA verifier for phone authentication."
  [state-atom]
  (when-not (:recaptcha-verifier @state-atom)
    (let [verifier (fb/get-recaptcha-verifier
                    recaptcha-container-id
                    #js {:size "invisible"
                         :callback (fn [_]
                                     (println "reCAPTCHA resolved"))
                         :expired-callback (fn []
                                             (println "reCAPTCHA expired, resetting...")
                                             (unset-recaptcha-verifier! state-atom))})]
      (.render verifier)
      (set-recaptcha-verifier! state-atom verifier))))

(defn- start-mfa-flow!
  "Initiates the MFA flow when the login response requires it."
  [state-atom result]
  (println "MFA required, initiating phone verification...")
  (let [resolver (fb-auth/getMultiFactorResolver (fb/get-auth) result)
        hints (->clj (.-hints resolver))
        phone-hint (first (filter #(= (.-factorId %) fb-auth/PhoneMultiFactorGenerator.FACTOR_ID)
                                  hints))]
    (if phone-hint
      (do
        (set-mfa-resolver-and-hint! state-atom resolver phone-hint)
        (init-recaptcha state-atom)
        (go-try
         (let [verification-id (<! (fb/<?verify-phone-number-for-mfa! resolver phone-hint (:recaptcha-verifier @state-atom)))]
           (handle-mfa-verification-sent! state-atom verification-id))))
      (println "No phone factor hint found."))))

(defn- mfa-required? [error]
  (and (instance? js/Error error)
       (= (.-code error) "auth/multi-factor-auth-required")))

;; --- Validation Functions (thrown to form) ---

(defn- handle-cred-submit!
  "Attempts email/password login; throws on error for form to catch."
  [state-atom]
  (let [{:keys [email password]} @state-atom]
    (assert (and email password))
    (go-try
     (let [result (<! (fb/<?do-email-login! email password))]
       (if (instance? js/Error result)
         (if (mfa-required? result)
           (start-mfa-flow! state-atom result)
           (throw result))
         (handle-login-success!))))))

(defn- handle-mfa-submit!
  "Attempts OTP-code MFA sign-in; throws on error for form to catch."
  [state-atom]
  (let [{:keys [resolver verification-id otp-code]} @state-atom]
    (assert (and resolver verification-id otp-code))
    (go-try
     (let [result (<! (fb/<?resolve-mfa-signin! resolver verification-id otp-code))]
       (if (instance? js/Error result)
         (if (invalid-code-error? result)
           (throw (js/Error. "Neteisingas SMS kodas."))
           (throw result))
         (handle-login-success!))))))

;; --- Login Page Component ---

(defn login-page
  []
  (r/with-let [state-atom
               (r/atom {:step :enter-credentials
                        :error nil})]
    (let [step (:step @state-atom)
          credential-phase? (= step :enter-credentials)
          credentials-hidden? (not credential-phase?)
          mfa-phase? (= step :enter-mfa-code)
          mfa-hidden? (not mfa-phase?)]
      [:<>
       [form
        {:state-atom state-atom
         :<?valid-fn
         (fn [_]
           (case step
             :enter-credentials (handle-cred-submit! state-atom)
             :enter-mfa-code (handle-mfa-submit! state-atom)))}
        [:div {:hidden credentials-hidden?}
         [labeled-input
          {:label "El. paštas" :state-atom state-atom :state-key :email
           :input-type :login-email :required credential-phase?}]]
        [:div {:hidden credentials-hidden?}
         [labeled-input
          {:label "Slaptažodis" :state-atom state-atom :state-key :password
           :input-type :password :required credential-phase?}]]
        [:div {:hidden mfa-hidden?}
         [:p.mb-2 "Įveskite SMS kodą, išsiųstą į jūsų telefoną."]]
        [:div {:hidden mfa-hidden?}
         [labeled-input
          {:label "SMS Kodas" :state-atom state-atom :state-key :otp-code
           :input-type :otp :required mfa-phase?}]]]
       [:div {:id recaptcha-container-id}]])))


(defn must-reset-password-page
  "Page when user must reset their password (has :must-reset-password claim set)."
  []
  (r/with-let [state-atom (r/atom {:new-password nil})]
    [:<>
     [form
      {:state-atom state-atom
       :<?valid-fn
       (fn [current-form-state-atom]
         (go-try
          (let [new-password (:new-password @current-form-state-atom)
                result (<! (cloud-fn/<?user-change-own-password! new-password))]
            (if (instance? js/Error result)
              (let [error-code (.-code result)
                    display-message (cond
                                      (= error-code "auth/weak-password")
                                      "Slaptažodis per silpnas. Jis turi būti bent 6 simbolių ilgio."
                                      (= error-code "auth/requires-recent-login")
                                      "Šiai operacijai reikia neseno prisijungimo. Prašome prisijungti iš naujo."
                                      :else (str "Įvyko netikėta klaida keičiant slaptažodį ("
                                                 (or error-code (.-message result)) "). Bandykite dar kartą."))]
                (throw (js/Error. display-message)))
              ;; After successful password change, log the user out
              ;; This is necessary because the server revokes refresh tokens, but doesn't immediately sign out the user
              (do
                (js/alert "Slaptažodis sėkmingai pakeistas. Jums reikės prisijungti iš naujo.")
                (fb/<logout-user!))))))}
      [views-core/header "Pakeisti slaptažodį"]
      [:p "Jūsų slaptažodis turi būti pakeistas. Pasirinkite naują slaptažodį."]
      [labeled-input
       {:label "Naujas slaptažodis"
        :state-atom state-atom
        :state-key :new-password
        :input-type :password
        :required true}]]]))