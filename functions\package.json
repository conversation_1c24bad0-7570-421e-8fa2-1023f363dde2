{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"axios": "^1.7.9", "firebase": "^11.2.0", "firebase-admin": "^12.1.0", "firebase-functions": "^6.3.2", "jiff": "^0.7.3", "object-hash": "^3.0.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}