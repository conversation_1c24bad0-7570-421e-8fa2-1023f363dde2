(ns medplast.core
    (:require
      [medplast.views.pages :as pages]
      [medplast.routes :as routes]
      [medplast.connectivity :as connectivity]
      [reagent.dom :as d]
      ))

;; -------------------------
;; Initialize app

(defn mount-root []
  (d/render [pages/page-container] (.getElementById js/document "app")))

(defn ^:export init! []
  (routes/start-router!)
  (connectivity/init-connectivity-monitoring!)
  (mount-root)
  )
