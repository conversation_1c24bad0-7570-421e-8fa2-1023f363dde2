(ns medplast.routes
  (:require
   ; [cljs.pprint :rename {pprint println}]
   [medplast.views.grids.user :as grid-user]
   [medplast.views.forms.user :as form-user]
   [medplast.views.grids.product :as grid-product]
   [medplast.views.forms.product :as form-product]
   [medplast.views.grids.sale :as grid-sale]
   [medplast.views.forms.sale :as form-sale]
   [medplast.views.forms.login :as form-login]
   [medplast.views.grids.patient :as grid-patient]
   [medplast.views.grids.statistics :as grid-statistics]
   [medplast.views.grids.audit :as grid-audit]
   [medplast.views.forms.patient :as form-patient]
   [medplast.views.forms.sign :as form-sign]
   [medplast.views.forms.registration :as form-registration]
   [medplast.views.grids.sign :as grid-sign]
   #_[medplast.views.core :as views-core]
   [medplast.views.pages :as pages]
   [medplast.state :as state]
      ; [reagent.core :as r]
      ; [reitit.core :as re]
   [reitit.frontend :as rf]
   [reitit.frontend.easy :as rfe]))

(def namespace-string (namespace ::x))

; could use this for getting route keywords in other namespaces
(defn get-kw-for-action [id-kw action-kw]
  ; accepts both strings and keywords (name acts as identity for strings)
  (let [id (name id-kw)
        action (name action-kw)]
    (keyword namespace-string (str id "-" action))))

(defn make-resource-routes [& {:as spec}]
  ; using string value of id keyword as prefix, creates sub-routes
  ; /list, /add, /view/:id, /edit/:id
  (let [id (name (:id spec))
        views (:views spec)
        ; User-relative means that the route needs user-id to function.
        root-url (str "/" id)
        make-sub-url
        (fn [action]
          (let [action-str (name action)]
            (case action
              (:list :add) (str "/" action-str)
              (:edit :view) (str "/" action-str
                                 "/:id"))))
        sub-endpoints
        (for [action [:list :add :view :edit]]
          [(make-sub-url action)
           {:name (get-kw-for-action id action)
            :previous-breadcrumb (case action
                                   :list ::frontpage
                                   (get-kw-for-action id :list))
            :view (get views action)}])
        result
        ; NOTE need vec, because reitit fails if its a Cons structure
        (vec (cons root-url sub-endpoints))]
    ;(println result)
    result))

; for explanation of #' syntax when refering to view functions
; see https://blog.valerauko.net/2022/12/03/hot-reloading-and-reitit-in-the-frontend/
; and https://stackoverflow.com/a/37158003
(def routes
  [["/"
    {:name ::frontpage
     :view #'pages/frontpage}]
   ["/statistics"
    {:name ::statistics
     :view #'grid-statistics/statistics-page}]
   ["/audit-log"
    {:name ::audit-log
     :view #'grid-audit/audit-page}]
   ; TODO remove
   ["/test"
    {:name ::testpage
     :view #'pages/testpage}]
   ["/login"
    {:name ::login
     :view #'form-login/login-page}]
   ["/logout"
    {:name ::logout
     :view #'pages/logout-page}]
   (make-resource-routes
    :id :user
    :views {:list #'grid-user/user-list-page
            ; :add #'form-user/user-add-page
            :view #'form-user/user-view-page
            :edit #'form-user/user-edit-page}) 
   ["/user/register"
    {:name ::user-register
     :previous-breadcrumb ::user-list
     :view #'form-registration/registration-page}] 
   ["/user/edit-email/:user"
    {:name ::user-email-edit
     :previous-breadcrumb ::user-edit 
     :view #'form-user/user-email-edit-page}]
   ["/user/edit-phone/:user"
    {:name ::user-phone-edit
     :previous-breadcrumb ::user-edit
     :view #'form-user/user-phone-edit-page}]
   ["/user/edit-display-name/:user"
    {:name ::user-display-name-edit
     :previous-breadcrumb ::user-edit
     :view #'form-user/user-display-name-edit-page}] 
  ["/user/reset-password/:user"
   {:name ::user-password-reset
    :previous-breadcrumb ::user-view
    :view #'form-user/user-password-reset-page}]
   ["/user/:user"
    (make-resource-routes
     :id :sale
     :views {:list #'grid-sale/sale-list-page
             :add #'form-sale/sale-add-page
             :view #'form-sale/sale-view-page
             :edit #'form-sale/sale-edit-page})
    ["/sale/replace/:id"
     {:name (get-kw-for-action "sale" :replace)
      :previous-breadcrumb (get-kw-for-action "sale" :list)
      :view #'form-sale/sale-replace-page}]
    (make-resource-routes
     :id :patient
     :views {:list #'grid-patient/patient-list-page
             :add #'form-patient/patient-add-page
             :view #'form-patient/patient-view-page
             :edit #'form-patient/patient-edit-page})
    ["/patient/move/:id"
     {:name (get-kw-for-action "patient" :move)
      :previous-breadcrumb (get-kw-for-action "patient" :list)
      :view #'form-patient/patient-move-page}]
    ["/patient/sign-list/:id"
     {:name (get-kw-for-action "patient" :sign-list)
      :previous-breadcrumb (get-kw-for-action "patient" :view)
      :view #'grid-sign/sign-list-page}]
    ["/patient/sign/:id/:form-template-slug"
     {:name (get-kw-for-action "patient" :sign)
      :view #'form-sign/sign-page}]]
   (make-resource-routes
    :id :product
    :views {:list #'grid-product/product-list-page
            :add #'form-product/product-add-page
            :view #'form-product/product-view-page
            :edit #'form-product/product-edit-page})])

(defn start-router! []
  (let [router (rf/router routes)]
    ;(println (re/routes router))
    (rfe/start!
     router
     (fn [m] (reset! state/route-match m))
     {:use-fragment false})))

;(defonce logger (r/track! #(println "route-match: " @state/route-match)))

; NOTE examples
; https://github.com/metosin/reitit/blob/master/examples/frontend/src/frontend/core.cljs
; https://github.com/metosin/reitit/blob/master/examples/frontend-controllers/src/frontend/core.cljs
