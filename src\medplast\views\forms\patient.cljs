(ns medplast.views.forms.patient
  (:require
   [medplast.lang :as lang]
   [medplast.views.forms.core :as forms-core
    :refer [form input labeled labeled-input checkbox]]
   [medplast.views.grids.sale :as grids-sale]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.state :as state]
   [medplast.firebase :as firebase]
   [medplast.cloud-fn :as cloud-fn]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]
   [cljc.java-time.local-date :as local-date]))

(defn- make-sure-doc-has-assignee-and-adder-set
  "Ensures a document map has `:assigned-to` and `:added-by` keys set.
   If these keys are not already present in the `doc` map, they are set to the provided `user-uid`.
   This is important for tracking which user is responsible for a document and who initially created it."
  [doc user-uid]
  (let [assigned-to (or (:assigned-to doc) user-uid)
        added-by (or (:sold-by doc) user-uid)]
    (assoc doc :assigned-to assigned-to :added-by added-by)))

(defn- <?add-or-overwrite-patient!
  "Asynchronously adds or overwrites a patient document in Firestore.
   It first ensures the document has the correct assignee and adder set using `make-sure-doc-has-assignee-and-adder-set`.
   It then uses the implied user's patient collection reference to perform the add/overwrite operation.
   This function is used for saving patient data to the database."
  [doc]
  (println "doing <?add-or-overwrite-patient!")
  (let [patient-col-ref @@(r/track state/get-implied-user-patient-col-ref-atom)
        user-uid @@(r/track state/get-implied-user-uid-atom)
        doc (make-sure-doc-has-assignee-and-adder-set doc user-uid)]
    (firestore/<?add-or-overwrite-doc! patient-col-ref doc)))

(defn- <?handle-valid-submit!
  "Handles the submission of a valid patient form.
   It uses the generic `forms-core/<?handle-valid-submit!` to manage the submission process.
   The `<?attempt-fn` is set to call `<?add-or-overwrite-patient!` with the current form state.
   Upon successful submission, it navigates back using `views-core/go-back!`.
   This function connects the patient form's valid state to the action of saving the patient data."
  [form-state-atom]
  (forms-core/<?handle-valid-submit!
   {:<?attempt-fn #(<?add-or-overwrite-patient! @form-state-atom)
    :on-success #(views-core/go-back!)}))

(defn- action-dropdown
  "A Reagent component for a dropdown menu that triggers actions based on the selected option.
   It displays a prompt and a list of labels. When a label is selected, the corresponding action function from `label-to-action-map` is executed.
   This component provides a way to offer a list of actions related to an item, such as making a contact primary or deleting it."
  [{:keys [css-classes prompt label-to-action-map]}]
  (let [css-classes (or css-classes "")
        prompt (or prompt "Veiksmai:")
        action-dropdown-change-handler
        (fn [e]
          (let [label-clicked (.. e -target -value)
                was-prompt-clicked (= prompt label-clicked)]
            (when-not was-prompt-clicked
              (when-let [action (get label-to-action-map label-clicked)]
                (action))))
          )]
    [:select {:class css-classes
              :value prompt
              :on-change action-dropdown-change-handler}
     [:<>
      (when prompt
        [:option {:disabled true :value prompt} prompt])
      (for [label (keys label-to-action-map)]
        ^{:key label} [:option {:value label} label])]]))

; TODO used by sibling namespace, move to some parent namespace
(defn dynamic-input-list
  "A Reagent component for managing a dynamic list of input fields, typically used for contacts or addresses.
   It binds to a state cursor pointing to a map where keys are keywordized integers and values are the input strings.
   Allows adding new input fields and, when not in read-only mode, provides an action dropdown for each item to make it primary or delete it.
   The 'primary' concept is implemented by moving the selected item to the first position in the map (sorted by keywordized integer key).
   This component is needed to handle variable-length lists of related information within a form."
  [{:keys [state-cursor read-only required]}]
  (let [required (or required false)
        initial {:0 ""}
        kw-ix-to-contact-map (or @state-cursor initial)
        kw-ix-to-contact-map
        (lang/sort-map-keys-by lang/keywordized-int-to-int kw-ix-to-contact-map)
        kw-ixes (keys kw-ix-to-contact-map)
        last-kw-ix (last kw-ixes)
        new-kw-ix (lang/map-keywordized-int inc last-kw-ix)
        new-contact-subcursor (r/cursor state-cursor [new-kw-ix])
        add-new-contact #(reset! new-contact-subcursor "")
        last-contact-is-some (not-empty (get kw-ix-to-contact-map last-kw-ix))
        no-contacts (empty? kw-ixes)
        should-activate-new-contact-button
        (and (not read-only) (or last-contact-is-some no-contacts))
        should-display-empty-field (and read-only no-contacts)
        first-kw-ix (-> kw-ix-to-contact-map first key)
        make-primary
        (fn [kw-ix]
          (swap! state-cursor
                 lang/swap-key kw-ix
                 (lang/map-keywordized-int dec first-kw-ix)))
        remove-contact (fn [kw-ix]
                         (swap! state-cursor dissoc kw-ix))
        single-contact (>= 1 (count kw-ixes))
        should-show-action-dropdown
        (and (not read-only) (not single-contact))]
    [:<>
     (for [kw-ix kw-ixes]
       (let [subcursor (r/cursor state-cursor [kw-ix])
             primary (= kw-ix first-kw-ix)
             make-primary-action-pair (if-not primary
                                        ["Padaryti pagrindiniu" (partial make-primary kw-ix)]
                                        nil)
             label-to-action-pairs (concat
                                    make-primary-action-pair
                                    ["Ištrinti" (partial remove-contact kw-ix)])]

         ^{:key kw-ix}
         [:div {:class "flex flex-row"}
          [input {:state-cursor subcursor
                  :read-only read-only
                  :required required
                  :input-type :str}]
          (when should-show-action-dropdown
            [action-dropdown
             {:css-classes "w-2 border-l-0"
              :label-to-action-map
              (apply sorted-map label-to-action-pairs)}])]))
     (when should-activate-new-contact-button
       [views-core/on-click-link add-new-contact "Pridėti naują"])
     (when should-display-empty-field
       [input {:state-cursor (r/cursor state-cursor [:dummy])
               :read-only read-only
               :input-type :str}])]))

(defn- patient-form
  "A Reagent component for rendering the patient form.
   It uses the generic `form` component and includes various labeled inputs and checkboxes for patient details.
   It also includes a watch (`forms-core/watch-inactive-constraint`) to automatically set the patient as inactive if certain conditions (deceased, switched to competitor, stoma closed, or invalid) are met.
   This component provides the structure and fields for both adding and editing patient information."
  [{:keys [state-atom read-only hide-set]}]
  (r/with-let [remove-inactive-validity-constraint
               (forms-core/watch-inactive-constraint
                state-atom read-only
                [:invalid :deceased :switched-to-competitor :stoma-closed])]
    [form
     {:state-atom state-atom :read-only read-only :<?valid-fn <?handle-valid-submit!}
     [labeled-input
      {:label "Vardas"
       :state-key :first-name
       :input-type :str
       :state-atom state-atom
       :read-only read-only
       :input-attrs {:required true}}]
     [labeled-input
      {:label "Pavardė"
       :state-key :last-name
       :input-type :str
       :state-atom state-atom
       :read-only read-only
       :input-attrs {:required true}}]
     [labeled {:label "Kontaktai"}
      [dynamic-input-list
       {:state-cursor (r/cursor state-atom [:contacts])
        :required true
        :read-only read-only}]]
     [labeled {:label "Adresai"}
      [dynamic-input-list
       {:state-cursor (r/cursor state-atom [:addresses])
        :read-only read-only}]]
     [labeled-input
      {:label "Stomos tipas"
       :state-key :stoma-type
       :input-type :str
       :state-atom state-atom
       :read-only read-only
       :datalist ["2 stomos" "Ileostoma" "Kolostoma" "Urostoma"]}]
     [labeled-input
      {:label "Vaistinė"
       :state-key :pharmacy
       :input-type :str
       :state-atom state-atom
       :read-only read-only}]
     [labeled {:label "Kam priskirta" :hide-set hide-set :state-key :assigned-to}
      [forms-core/user-select
       {:state-key :assigned-to
        :state-atom state-atom
        :read-only true
        :hide-set hide-set}]]
     [labeled {:label "Kas sukūrė" :hide-set hide-set :state-key :added-by}
      [forms-core/user-select
       {:state-key :added-by
        :state-atom state-atom
        :read-only true
        :hide-set hide-set}]]
     [labeled-input
      {:label "Sukūrimo data"
       :state-key :addition-date
       :input-type :date
       :state-atom state-atom
       :hide-set hide-set
       :read-only true}]
     [labeled {:label "Komentarai"}
      [input
       {:state-key :comments
        :input-type :long-str
        :state-atom state-atom
        :read-only read-only}]]
     [labeled {:label "Pasirašė?" :hide-set hide-set :state-key :signed}
      [:<>
       [checkbox
        {:state-key :signed
         :state-atom state-atom
         :read-only read-only}]]]
     [labeled {:label "Aktyvus?" :hide-set hide-set :state-key :active}
      [:<>
       [checkbox
        {:state-key :active
         :state-atom state-atom
         :read-only true}]
       (when (not read-only)
         [forms-core/clarification "Aktyvumas priklauso nuo žemiau esančių laukelių."])]]
     [labeled {:label "Miręs?" :hide-set hide-set :state-key :deceased}
      [checkbox
       {:state-key :deceased
        :state-atom state-atom
        :read-only read-only}]]
     [labeled {:label "Išėjęs pas konkurentus?" :hide-set hide-set :state-key :switched-to-competitor}
      [checkbox
       {:state-key :switched-to-competitor
        :state-atom state-atom
        :read-only read-only}]]
     [labeled {:label "Uždaryta stoma?" :hide-set hide-set :state-key :stoma-closed}
      [checkbox
       {:state-key :stoma-closed
        :state-atom state-atom
        :read-only read-only}]]
     [labeled {:label "Klaidingas?" :hide-set hide-set :state-key :invalid}
      [checkbox
       {:state-key :invalid
        :state-atom state-atom
        :read-only read-only}]]]
    (finally (remove-inactive-validity-constraint))))

(def initial-state
  "A map defining the initial state for a new patient form.
   Includes default values for various patient attributes like addition date, signed status, active status, and different reasons for inactivity.
   This map is used to initialize the state atom when adding a new patient."
  {:addition-date (str (local-date/now))
   :signed false
   :active true
   :invalid false
   :stoma-closed false
   :deceased false
   :switched-to-competitor false})

(defn patient-add-page
  "The Reagent component for the patient add page.
   It initializes a state atom with `initial-state` and renders the `patient-form` component.
   It also includes a header with a back button.
   Certain fields are hidden in the add form using the `:hide-set` option."
  []
  (r/with-let [empty-state-atom (r/atom initial-state)]
    [:<>
     [header "Pridėti pacientą"
      {:on-click views-core/go-back! :text "Atgal"}]
     [patient-form {:state-atom empty-state-atom
                    :hide-set
                    #{:signed :active :deceased :switched-to-competitor :stoma-closed :invalid :added-by :assigned-to :addition-date}}]]))

(defn patient-view-page
  "The Reagent component for the patient view page.
   It fetches patient data based on the route parameter ID using a Firestore subscription.
   It displays the patient information using the `patient-form` in read-only mode.
   Includes a header with links to edit, move, and view signatures for the patient.
   Also displays a table of the patient's sales."
  [_match]
  (r/with-let [id-atom state/route-match-doc-id-atom
               doc-subscription-atom
               (r/reaction
                (when-let [patient-col-ref @@(r/track state/get-implied-user-patient-col-ref-atom)]
                  @@(r/track
                     firestore/get-doc-subscription-atom
                     (firestore/document-ref patient-col-ref @id-atom))))]
    (let [id @id-atom
          doc @doc-subscription-atom
          active (:active doc)]
      [:<>
       [views-core/header "Peržiūrėti pacientą"
        {:on-click views-core/go-back! :text "Atgal"}
        {:href (rfe/href :medplast.routes/patient-edit
                         {:user @@(r/track state/get-implied-user-uid-atom) :id id})
         :text "Redaguoti"}
        {:href (rfe/href :medplast.routes/patient-move
                         {:user (views-core/get-user-uid-from-patient doc) :id id})
         :text "Perkelti"}
        {:href (rfe/href :medplast.routes/patient-sign-list
                         {:user (views-core/get-user-uid-from-patient doc) :id id})
         :text "Pasirašymai"}]
       (case doc
         ::firebase/waiting [:span "Siunčiama..."]
         nil [:span "Nerasta ir/arba perkelta."]
         [:<>
          [patient-form {:state-atom doc-subscription-atom
                         :read-only true}]
          [header "Paciento pardavimai"
           (when active
             {:href (rfe/href :medplast.routes/sale-add
                              {:user (views-core/get-user-uid-from-patient doc)}
                              {:patient id})
              :text "Pridėti pardavimą"})]
          [grids-sale/patient-sales-table id]])])))

(defn patient-edit-page
  "The Reagent component for the patient edit page.
   It fetches patient data based on the route parameter ID using `firestore/get-doc-atom`.
   It displays the patient information using the `patient-form` in editable mode.
   Includes a header with links to view and move the patient."
  [_match]
  (r/with-let [id-atom state/route-match-doc-id-atom
               patient-col-ref-atom @(r/track state/get-implied-user-patient-col-ref-atom)
               doc-atom
               @(r/reaction
                 (firestore/get-doc-atom
                  (firestore/document-ref @patient-col-ref-atom @id-atom)))]
    (when doc-atom
      (let [id @id-atom
            doc @doc-atom]
        [:<>
         [header "Redaguoti pacientą"
          {:on-click views-core/go-back! :text "Atgal"}
          {:href (rfe/href :medplast.routes/patient-view
                           {:user (views-core/get-user-uid-from-patient doc) :id id})
           :text "Peržiūrėti"}
          {:href (rfe/href :medplast.routes/patient-move
                           {:user (views-core/get-user-uid-from-patient doc) :id id})
           :text "Perkelti"}]
         (cond
           (nil? doc) [:span "Nerasta ir/arba perkelta."]
           :else
           [patient-form {:state-atom doc-atom}])]))))

(defn patient-move-page
  "The Reagent component for the patient move page.
   It fetches the patient data using a Firestore subscription.
   It provides a form to select a new user to assign the patient to and handles the patient moving logic using `firebase/<?move-patient!`.
   Includes a header with links to view and edit the patient.
   This page is needed to transfer a patient and their associated sales from one user to another."
  [_match]
  (r/with-let
    [id-atom state/route-match-doc-id-atom
     doc-subscription-atom
     (r/reaction
      (when-let [patient-col-ref @@(r/track state/get-implied-user-patient-col-ref-atom)]
        @@(r/track
           firestore/get-doc-subscription-atom
           (firestore/document-ref patient-col-ref @id-atom))))
     state-atom (r/atom {})
     <?move-patient!
     (fn []
       (println "doing <?move-patient!")
       (let [from-user-uid @@(r/track state/get-implied-user-uid-atom)
             to-user-uid (:to-user-uid @state-atom)
             patient-uid @id-atom]
         (cloud-fn/<?move-patient! from-user-uid to-user-uid patient-uid)))
     <?handle-valid-submit!
     (fn []
       (forms-core/<?handle-valid-submit!
        {:<?attempt-fn #(<?move-patient!)
         :on-success #(views-core/go-back!)}))]
    (when doc-subscription-atom
      (let [id @id-atom
            doc @doc-subscription-atom]
        [:<>
         [header "Perkelti pacientą"
          {:on-click views-core/go-back! :text "Atgal"}
          {:href (rfe/href :medplast.routes/patient-view
                           {:user (views-core/get-user-uid-from-patient doc) :id id})
           :text "Peržiūrėti"}
          {:href (rfe/href :medplast.routes/patient-edit
                           {:user @@(r/track state/get-implied-user-uid-atom) :id id})
           :text "Redaguoti"}
          {:href (rfe/href :medplast.routes/patient-move
                           {:user (views-core/get-user-uid-from-patient doc) :id id})
           :text "Perkelti"}]
         (case doc
           ::firebase/waiting [:span "Siunčiama..."]
           nil [:span "Nerasta ir/arba perkelta."]
           [form
            {:state-atom state-atom
             :<?valid-fn <?handle-valid-submit!}
            [:p "Paspaudus \"Pateikti\", šis pacientas (" [:span {:class "font-bold"} (:last-name doc) ", " (:first-name doc)] "), ir su juo susieti pardavimai, bus perkelti žemiau pasirinktam vartotojui."]
            [labeled {:label "Kam priskirti"}
             [forms-core/user-select
              {:state-key :to-user-uid
               :required true
               :state-atom state-atom}]]])]))))
