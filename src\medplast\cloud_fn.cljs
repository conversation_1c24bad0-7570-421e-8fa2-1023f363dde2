; Holds firebase cloud function wrappers; these functions are meant to be called only through these wrappers.

(ns medplast.cloud-fn
  (:require
   [env :as env]
   [medplast.lang :refer-macros [<?] :refer [<?js-promise-to-chan go-try]]
   [cljs-bean.core :refer [->clj ->js]]
   [medplast.firebase :refer [firebase-app]]
   ["firebase/functions" :as functions]))

(defonce function-region "europe-west1")

(defonce ^:private firebase-functions
  (let [fns (functions/getFunctions firebase-app function-region)]
    (when env/dev
      (let [emulator-conf (env/get :firebase-emulator)
            host (:host emulator-conf)
            port (js/parseInt (:functions-port emulator-conf))]
        (functions/connectFunctionsEmulator
         fns
         host
         port)))
    fns))

(defn- <?get-data-from-result [result-c]
  "Extracts the 'data' field from the result of a Firebase Cloud Function call.
   At least some Cloud Function calls seem to return their result wrapped in the 'data' field."
  (go-try
   (let [result (<? result-c)
         result (->clj result)
         data (:data result)]
     data)))

(defonce ^:private set-user-activity-callable
  (functions/httpsCallable firebase-functions "setUserActivity"))

(defn <?set-user-activity!
  "Calls the 'setUserActivity' Firebase Cloud Function.
  This function is used to update a user's activity status (e.g., online/offline).
  `uid` is the target user's ID.
  `active` is a boolean indicating the activity status.
  Returns a channel that yields the result of the function call."
  [uid active]
  (let [promise (set-user-activity-callable
                 (->js {:uid uid :active active}))]
    (<?js-promise-to-chan promise)))

(defonce ^:private docuseal-proxy-callable
  (functions/httpsCallable firebase-functions "docusealProxy"))

(defn <?call-docuseal-proxy
  "Calls the 'docusealProxy' Firebase Cloud Function.
  This function acts as a proxy to interact with the DocuSeal API from the backend,
  avoiding CORS issues and potentially handling API key security.
  `url` is the DocuSeal API endpoint path.
  `body` is the request body for the DocuSeal API call.
  Returns a channel that yields the result of the function call."
  [url body]
  (println "<?call-docuseal-proxy" url body)
  (let [promise (docuseal-proxy-callable
                 (->js {:url url :body body}))]
    (<?js-promise-to-chan promise)))

(defonce ^:private move-patient-callable
  (functions/httpsCallable firebase-functions "movePatient"))

(defn <?move-patient!
  "Calls the 'movePatient' Firebase Cloud Function.
  This function is used to transfer a patient record from one user to another.
  `from-user-uid` is the UID of the current owner.
  `to-user-uid` is the UID of the new owner.
  `patient-uid` is the UID of the patient to move.
  Returns a channel that yields the result of the function call."
  [from-user-uid to-user-uid patient-uid]
  (let [promise (move-patient-callable
                 (->js {:from-user from-user-uid
                        :to-user to-user-uid
                        :patient patient-uid}))]
    (<?js-promise-to-chan promise)))

(defonce ^:private user-change-own-password-callable
  (functions/httpsCallable firebase-functions "userChangeOwnPassword"))

(defonce ^:private create-new-user-callable
  (functions/httpsCallable firebase-functions "createNewUser"))

(defn <?create-new-user!
  "Calls the 'createNewUser' Firebase Cloud Function.
   This function is used to create a new user with email, display name, and phone number.
   Returns a channel that yields the result of the function call, including the temporary password."
  [email display-name phone-number]
  (let [promise (create-new-user-callable
                 (->js {:email email
                        :displayName display-name
                        :phoneNumber phone-number}))]
    (<?js-promise-to-chan promise)))

(defn <?user-change-own-password!
  "Calls the 'userChangeOwnPassword' Firebase Cloud Function.
  This function is used by a user to change their own password,
  especially when the 'mustResetPassword' claim is true.
  `new-password` is the new password string."
  [new-password]
  (let [promise (user-change-own-password-callable
                 (->js {:newPassword new-password}))]
    (-> promise
        <?js-promise-to-chan
        <?get-data-from-result)))

(defonce ^:private admin-change-user-email-callable
  (functions/httpsCallable firebase-functions "adminChangeUserEmail"))

(defn <?admin-change-user-email!
  "Calls the 'adminChangeUserEmail' Firebase Cloud Function.
   Admin-only function to change a user's email.
   `uid` is the target user's UID.
   `new-email` is the new email string."
  [uid new-email]
  (println "<?admin-change-user-email!" uid new-email)
  (let [payload (->js {:uid      uid
                       :newEmail new-email})
        promise (admin-change-user-email-callable payload)]
    (<?js-promise-to-chan promise)))

(defonce ^:private admin-change-user-phone-callable
  (functions/httpsCallable firebase-functions "adminChangeUserPhoneNumber"))

(defn <?admin-change-user-phone-number!
  "Calls the 'adminChangeUserPhoneNumber' Firebase Cloud Function.
   Admin-only function to change a user's phone number (E.164 format).
   `uid` is the target user's UID.
   `new-phone-number` is the new phone number string (must start with '+')."
  [uid new-phone-number]
  (println "<?admin-change-user-phone-number!" uid new-phone-number)
  (let [payload (->js {:uid           uid
                       :newPhoneNumber new-phone-number})
        promise (admin-change-user-phone-callable payload)]
    (<?js-promise-to-chan promise)))

(defonce ^:private admin-reset-user-temporary-password-callable
  (functions/httpsCallable firebase-functions "adminResetUserTemporaryPassword"))

(defn <?admin-reset-user-temporary-password!
  "Calls the 'adminResetUserTemporaryPassword' Firebase Cloud Function.
   Admin-only function to reset a user's password to a temporary one and
   set a claim requiring them to change it on next login.
   `uid` is the target user's UID.
   Returns a channel that yields the result map, e.g.
   {:success true :message \"User password reset successfully...\" :temporaryPassword \"...\"}."
  [uid]
  (let [payload (->js {:uid uid})
        promise (admin-reset-user-temporary-password-callable payload)]
    (go-try
     (let [c (<?js-promise-to-chan promise)]
       (<?get-data-from-result c)))))

(defonce ^:private admin-change-user-display-name-callable
  (functions/httpsCallable firebase-functions "adminChangeUserDisplayName"))

(defn <?admin-change-user-display-name!
  "Calls the 'adminChangeUserDisplayName' Firebase Cloud Function.
   Admin-only function to change a user's display name.
   `uid` is the target user's UID.
   `new-display-name` is the new display name string (can be null)."
  [uid new-display-name]
  (println "<?admin-change-user-display-name!" uid new-display-name)
  (let [payload (->js {:uid uid
                       :newDisplayName new-display-name})
        promise (admin-change-user-display-name-callable payload)]
    (<?js-promise-to-chan promise)))