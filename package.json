{"scripts": {"shadow-browser-repl": "npx shadow-cljs browser-repl", "shadow-node-repl": "npx shadow-cljs node-repl", "shadow-watch": "npx shadow-cljs watch app", "shadow-release": "npx shadow-cljs release app", "shadow-compile": "npx shadow-cljs compile app", "shadow-check": "npx shadow-cljs check app", "tailwind-build": "npx tailwindcss -i ./src/css/app.css -o ./public/css/app.css", "tailwind-watch": "npx tailwindcss -i ./src/css/app.css -o ./public/css/app.css --watch", "tailwind-release": "npx tailwindcss -i ./src/css/app.css -o ./public/css/app.css --minify", "firebase-emulators-start": "npx firebase emulators:start --import ./emulator-saves/default", "firebase-emulators-start-empty": "npx firebase emulators:start", "firebase-emulators-export": "npx firebase emulators:export", "firebase-functions-shell": "firebase functions:shell"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "caniuse-lite": "^1.0.30001714", "firebase-tools": "^13.35.1", "postcss-import": "^16.1.0", "shadow-cljs": "^2.19.2", "tailwindcss": "^3.2.7"}, "dependencies": {"@docuseal/react": "^1.0.50", "@js-joda/core": "3.2.0", "ag-grid-community": "^31.3.2", "ag-grid-react": "^31.3.2", "firebase": "^10.12.1", "firebase-functions": "^6.3.2", "firebaseui": "^6.1.0", "jiff": "^0.7.3", "json-diff": "^1.0.6", "json-diff-kit": "^1.0.30", "random-avatar-generator": "github:dmos62/random-avatar-generator", "react": "^17.0.2", "react-dom": "^17.0.2", "tailwind-hamburgers": "^1.3.5"}}