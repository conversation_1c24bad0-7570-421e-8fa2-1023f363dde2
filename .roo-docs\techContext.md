## Languages & Runtimes

*   **Primary:** ClojureScript (frontend), Clojure (build/config/shared code - `.clj`, `.cljc`), JavaScript (Firebase Functions backend - `functions/index.js`).
*   **Runtime:** Node.js (for build tools, Firebase Functions), Browser JavaScript Engine (for frontend).

## Frameworks

*   **Frontend:** Likely Reagent and/or Re-frame (Common in ClojureScript SPAs, though not explicitly confirmed without reading code content).
*   **CSS:** Tailwind CSS (`tailwind.config.js`).
*   **Form Framework:** The framework in `src/medplast/views/forms/core.cljs` automatically includes a submit button unless the `read-only` flag is set.

## Key Libraries & Dependencies

*   **Build/Development:** `shadow-cljs` (`shadow-cljs.edn`), `npm` (`package.json`, `package-lock.json`).
*   **Frontend JS Dependencies (from `package.json`):** [Need to inspect `package.json` content for specifics - e.g., react, react-dom, firebase client SDKs, date-fns, etc.] - *Could not list specific JS libraries without reading `package.json`.*
*   **ClojureScript Dependencies (from `shadow-cljs.edn`):** [Need to inspect `shadow-cljs.edn` content for specifics - e.g., reagent, re-frame, cljs-http, etc.] - *Could not list specific CLJS libraries without reading `shadow-cljs.edn`.*
*   **Backend Dependencies (from `functions/package.json`):** `firebase-admin`, `firebase-functions`. [Need to inspect `functions/package.json` for others.] - *Could not list specific backend JS libraries without reading `functions/package.json`.*

## Data Stores

*   **Primary:** Google Firestore (`firebase.json`, `firestore.rules`, `firestore.indexes.json`, `src/medplast/firestore.cljs`).
*   **Configuration/State:** Filesystem (`.firebaserc`, `firebase.json`, `shadow-cljs.edn`, etc.), potentially browser local storage/session storage for frontend state.

## Technical Constraints (Observed)

*   **Platform:** Web Browser.
*   **Infrastructure:** Tied to the Firebase ecosystem (Hosting, Auth, Firestore, Functions). Requires Node.js for development/deployment.
*   **Build Process:** Relies on `shadow-cljs` and `npm`.
### State Management, Persistence, and Views Relationship

Based on the analysis of `src/medplast/state.cljs`, `src/medplast/firebase.cljs`, `src/medplast/firestore.cljs`, `src/medplast/views/grids/sale.cljs`, and `src/medplast/views/forms/sale.cljs`, the following relationships and patterns are observed:

- **`medplast.state`**: Serves as the central hub for application state using Reagent atoms. It orchestrates data flow by subscribing to real-time updates from Firestore via `medplast.firestore` and integrating Firebase Authentication state from `medplast.firebase`. It provides reactive atoms that hold the application's data, which are then consumed by the view components. This layer is responsible for managing the current state of various data entities (users, sales, patients, products) and derived data based on user roles or route parameters.

- **`medplast.firebase`**: Encapsulates the low-level interactions with Firebase Authentication and Firebase Cloud Functions. It provides functions for user authentication, managing auth state subscriptions, and calling backend functions. `medplast.state` utilizes the auth state subscriptions to maintain the logged-in user's information reactively, including custom claims like admin/active status. Cloud Functions are used for backend operations that require elevated privileges or server-side logic (e.g., moving patients, updating user auth details).

- **`medplast.firestore`**: Provides a structured ClojureScript API for interacting with Google Cloud Firestore. It handles the creation of collection and document references, applying query constraints, setting up and managing real-time data subscriptions (`onSnapshot`), fetching data snapshots (`getDoc`, `getDocs`), and performing data manipulation operations (`addDoc`, `setDoc`, `updateDoc`). `medplast.state` extensively uses the subscription capabilities of `medplast.firestore` to keep its reactive atoms synchronized with the database. View components, such as the `stock-selector` in the sale form, may also directly interact with `medplast.firestore` to fetch non-reactive data needed for rendering.

- **Views (`medplast.views.grids.sale`, `medplast.views.forms.sale`, etc.)**: These components are responsible for the user interface. They consume the reactive state atoms exposed by `medplast.state` to display data and update dynamically. Views contain presentation logic, form handling, validation, and navigation. User interactions within views (e.g., submitting a form, clicking an action button) trigger functions that may update local component state, navigate to other routes, or initiate data changes by calling functions in `medplast.state` or directly in `medplast.firestore` (for mutations).

**High-Level Routine Placement:**

- **State Management & Data Synchronization:** `medplast.state` is the primary location for managing application state and synchronizing it with the backend.
- **Low-Level Persistence Interactions:** `medplast.firebase` and `medplast.firestore` handle the direct communication with Firebase services.
- **View Logic & User Interaction:** View files (`medplast.views.*`) contain the components and logic for rendering the UI, handling user input, and triggering actions based on user interaction.
- **Data Transformation for Views:** Functions within view files (like `process-table-data` in `medplast.views.grids.sale`) transform raw data from state atoms into a format suitable for display.
- **Business Logic (View-Specific):** Some business logic directly related to view behavior (e.g., `frozen?` in `medplast.views.forms.sale` determining form editability) resides within the view files, although core data manipulation logic is typically delegated to the state or persistence layers.
- **Form Submission & Data Mutation:** Form submission handlers in view files (`<?add-or-update-and-redirect!`, `<?replace-and-redirect!`) orchestrate the process of saving data, often utilizing functions from `medplast.state` to get relevant references and then calling mutation functions in `medplast.firestore`.

This structure promotes a separation of concerns, with `medplast.state` acting as a mediator between the UI and the data layers, providing a reactive data flow that simplifies view development.