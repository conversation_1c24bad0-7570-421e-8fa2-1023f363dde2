@import 'ag-grid-community/styles/ag-grid.css';
@import 'ag-grid-community/styles/ag-theme-quartz.css';
@import 'json-diff-kit/dist/viewer.css';

#app {
  /* allows absolutely positioning an h-full opacity screen for mobile menu, otherwise only topmost-viewport is covered */
  position: relative;
}

.ag-theme-quartz {
  --ag-odd-row-background-color: hsla(0, 0%, 0%, 0.01);
  --ag-checkbox-checked-color: gray;
  --ag-checkbox-unchecked-color: gray;
  --ag-checkbox-indeterminate-color: gray;
}

/*
.ag-cell-auto-height {
  --reduceLineHeightBy: 16px;
  line-height: calc(
    var(--ag-internal-calculated-line-height) - var(--reduceLineHeightBy)
  );
  padding-top: calc(var(--reduceLineHeightBy) / 2);
  padding-bottom: calc(var(--reduceLineHeightBy) / 2);
}
*/

/*
* :not(.ag-cell-auto-height) > .ag-cell-wrapper {
*/

.ag-cell-wrapper {
  --reduceLineHeightBy: calc(16px * 2.5);
  /* or should --ag-internal-calculated-line-height be used? */
  line-height: calc(
    var(--ag-internal-calculated-line-height) - var(--reduceLineHeightBy)
  );
  /*padding-top: calc(var(--reduceLineHeightBy) / 2);*/
  /*padding-bottom: calc(var(--reduceLineHeightBy) / 2);*/
  min-height: var(--ag-internal-calculated-line-height);
  @apply flex items-center;
}

.ag-row.line-through {
  --stripeColor: hsla(0, 100%, 88%, 1.0);
  --stripeWidth: 2px;
  /* --patternSize: 41px; */
  --patternSize: 21px;
  background-image: repeating-linear-gradient(
    -45deg,
    var(--stripeColor) 0,
    var(--stripeColor) var(--stripeWidth),
    transparent 0,
    transparent 50%);
  background-size: var(--patternSize) var(--patternSize);
}

.ag-row.line-through * {
  text-decoration: line-through !important;
} 

/* fix pagination footer layout on narrow screen */
.ag-paging-panel {
  height: auto;
  min-height: var(--ag-header-height);
  flex-wrap: wrap;
  @apply flex-wrap;
}

/* fix pagination footer layout on narrow screen */
.ag-paging-panel > span {
  @apply my-2
}

@tailwind base;
@tailwind components;
@tailwind utilities;
