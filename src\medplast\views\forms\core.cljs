(ns medplast.views.forms.core
  (:require
   [medplast.lang :refer-macros [<?] :refer [go-try]]
   [cljs.core.async :refer [go]]
   [clojure.core.async :refer [<!]]
   [clojure.edn]
   [medplast.firestore :as firestore]
   [medplast.state :as state]
   [medplast.views.core :as views-core]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]))

(defn- serialize
  "Serializes a value to a string using `pr-str`.
   This is used to convert ClojureScript values to strings for use as HTML input values,
   particularly in select options, as HTML option values are always strings.
   The intent is to preserve the original data type information when the value is later deserialized."
  [x] (pr-str x))

(defn- deserialize
  "Deserializes a string back into a ClojureScript value using `clojure.edn/read-string`.
   This is the inverse of `serialize`, used to convert string values from HTML inputs
   back into their original ClojureScript data types.
   It's needed because HTML input values are always strings, and we need to work with
   structured data in the application state."
  [x-str] (clojure.edn/read-string x-str))

(defn- make-input-handler
  "Creates an event handler function for input elements.
   The handler reads the input value from the event, converts it to the `expected-type`,
   and updates the provided `state-cursor` atom with the new value.
   It handles different expected types like `:deserialize` (for values serialized with `serialize`),
   `:int` (converting to number, handling NaN), and defaults to string.
   This function is crucial for binding form input values to application state managed by Reagent atoms."
  [state-cursor expected-type]
  (fn [^js/Event e]
    (let [string-value (.-target.value e)
          number-but-nan-is-nil
          #(if
            (empty? string-value)
             nil
             (.-target.valueAsNumber e))
          value
          (case expected-type
            :deserialize (deserialize string-value)
            :int (number-but-nan-is-nil)
            string-value)]
      (reset! state-cursor value)
      e)))

(def base-input-css-classes
  "block w-full")

(def input-css-classes
  (str base-input-css-classes
       " disabled:bg-slate-100 disabled:appearance-none disabled:resize-none"))

(def clickable-disabled-input-css-classes
  (str base-input-css-classes
       " cursor-pointer underline pointer-events-none"
       " text-blue-600 bg-slate-100"
       " disabled:appearance-none disabled:resize-none"))

(defn- update-prompt-input-validity
  "Workaround to ensure HTML 'required' validation works correctly for select inputs
   that include a disabled prompt option (e.g., 'Select an option...').
   When the select element's value matches the `prompt-value`, it manually sets the
   input's custom validity to the `error-message`, marking it as invalid.
   Otherwise, it clears the custom validity, marking it as valid.
   This is necessary because browsers typically consider a disabled option with a value
   as a valid selection for a required field, even if it's the prompt."
  [select-el prompt-value error-message]
  ; when select-el's value matches prompt-value, declares the input as invalid;
  ; otherwise, declares it valid. this is a workaround for disabled prompt
  ; inputs being treated as valid default selections, thus breaking "required"
  ; functionality.
  (let [string-value (.-value select-el)
        value-is-prompt-value (= prompt-value string-value)
        declare-invalid #(.setCustomValidity select-el error-message)
        declare-valid #(.setCustomValidity select-el "")]
    (if value-is-prompt-value
      (declare-invalid)
      (declare-valid))))

(defn select
  "A Reagent component for rendering an HTML `<select>` element.
   It binds the selected value to a Reagent atom (`state-cursor` or derived from `state-atom` and `state-key`).
   Supports read-only mode, displaying a prompt option, and optional linking (`value-to-href-fn`)
   when in read-only mode and a value is selected.
   Options are expected as a sequence of maps with `:value` and `:label`. Values are serialized
   before being set as HTML option values and deserialized when the input changes.
   Includes a workaround (`update-prompt-input-validity`) to handle 'required' validation with prompts."
  [_ _]
  (let [prompt-value ""
        select-el-atom (atom nil)
        handle-update
        #(when-let [select-el @select-el-atom]
           (update-prompt-input-validity select-el prompt-value "Required"))]
    (r/create-class
     {:display-name "select"
      :component-did-mount handle-update
      :component-did-update handle-update
      :reagent-render
      (fn [{:keys [state-cursor read-only state-atom state-key prompt
                   required value-to-href-fn]}
           options]
        (let [state-cursor (or state-cursor (r/cursor state-atom [state-key]))
              state-cursor-value
            ; should only serialize @state-cursor when it's not nil
              (let [state-cursor-value @state-cursor]
                (when (some? state-cursor-value)
                  (serialize state-cursor-value)))
              selected-value (or state-cursor-value prompt-value)
              is-editable (not read-only)
              is-nothing-selected (= selected-value prompt-value)
              should-display-prompt (and prompt is-editable is-nothing-selected)
              clickable (and
                         read-only
                         (not is-nothing-selected)
                         value-to-href-fn)
              options (map #(update % :value serialize) options)
              css-classes
              (if clickable
                clickable-disabled-input-css-classes
                input-css-classes)
              href (when clickable
                     (value-to-href-fn selected-value))
              input-handler (make-input-handler state-cursor :deserialize)
              select-element
              [:select {:class css-classes
                        :disabled read-only
                        :value selected-value
                        :ref (partial reset! select-el-atom)
                        :required required
                        :on-change input-handler}
               [:<>
                (when should-display-prompt
                  [:option {:disabled true :value prompt-value} prompt])
                (for [{:keys [label value]} options]
                  ^{:key value} [:option {:value value} label])]]]
          (if clickable
            [views-core/href-link href select-element]
            select-element)))})))

(defn textarea
  "A Reagent component for rendering an HTML `<textarea>` element.
   It automatically adjusts its height based on the content using the `scrollHeight` property.
   This provides a better user experience for multi-line text inputs, preventing the need for manual resizing.
   Uses `r/with-let` and watches to manage the DOM element and update height on input events."
  [attr-map]
  ; https://presumably.de/reagent-mysteries-part-3-manipulating-the-dom.html
  ; BUG should break if multiple textareas are rendered at once, because of with-let
  (r/with-let [el-atom (r/atom nil)
               scroll-height-atom (r/atom nil)
               reset-scroll-height-atom
               (fn []
                 (when-let [el @el-atom]
                   (let [scroll-height (.-scrollHeight el)
                         last-scroll-height @scroll-height-atom
                         would-enlarge (< last-scroll-height scroll-height)]
                     (when would-enlarge
                       (reset! scroll-height-atom scroll-height)))))
               event-name "input"
               subscribe-to-input-events
               (fn [el]
                 (.addEventListener el event-name reset-scroll-height-atom))
               unsubscribe-from-input-events
               (fn [el]
                 (.removeEventListener el event-name reset-scroll-height-atom))
               el-watch-fn
               (fn [_key _ref old-el new-el]
                 (when-let [el new-el]
                   (subscribe-to-input-events el)
                   (reset-scroll-height-atom))
                 (when-let [el old-el]
                   (unsubscribe-from-input-events el)))
               _ (add-watch el-atom :reset-scroll-height el-watch-fn)]
    (let [ref-attr {:ref (fn [el] (reset! el-atom el))}
          style-attr (when-let
                      [scroll-height @scroll-height-atom]
                       {:style {:height scroll-height}})
          attr-map (merge attr-map ref-attr style-attr)]
      [:textarea attr-map])
    (finally
      (when-let [el @el-atom]
        (unsubscribe-from-input-events el))
      (remove-watch el-atom :reset-scroll-height))))

(defn input
  "A Reagent component for rendering various HTML `<input>` elements.
   It binds the input value to a Reagent atom (`state-cursor` or derived from `state-atom` and `state-key`).
   Supports different `input-type` keywords (e.g., `:text`, `:number`, `:date`, `:doc-ref`, `:long-str`, `:select`),
   read-only mode, datalists for suggestions, and optional linking (`value-to-href-fn`)
   when in read-only mode and a value is present (specifically for `:doc-ref` types).
   Handles basic HTML input types and maps custom keywords like `:doc-ref` and `:long-str`
   to appropriate HTML elements or components (`textarea`)."
  [{:keys [state-cursor state-atom state-key input-type
           input-attrs read-only datalist
           value-to-href-fn required input-handler] :as _args}]
  (r/with-let [datalist-uuid (random-uuid)]
    (let [state-cursor (or state-cursor (r/cursor state-atom [state-key]))
          input-handler (or input-handler (make-input-handler state-cursor input-type))
          editable (not read-only)
          ; datalist should only be used when it's available and the input is editable
          datalist (when (and datalist editable) datalist)
          default ""
          required (or required false)
          value (or @state-cursor default)
          has-value (not= default value)
          clickable (and
                     read-only
                     has-value
                     value-to-href-fn)
          href (when clickable (value-to-href-fn value))
          input-state-attrs {:value value
                             :on-change input-handler}
          autocomplete {:autoComplete
                        (case input-type
                          :password "current-password"
                          :login-email "username"
                          :email "email"
                          :otp "one-time-code"
                          "off")}
          css-classes
          (if clickable
            clickable-disabled-input-css-classes
            input-css-classes)
          list-attr (when datalist {:list datalist-uuid})
          input-mode {:inputMode
                      (case input-type
                        :otp "numeric"
                        nil)}
          input-attrs (merge
                       input-state-attrs
                       {:class css-classes
                        :required required
                        :disabled read-only}
                       input-mode
                       autocomplete
                       input-attrs
                       list-attr)
          raw-input-type (case input-type
                           :doc-ref "text"
                           :date "date"
                           :datetime-local "datetime-local"
                           :password "password"
                           :otp "text"
                           :login-email "email"
                           :email "email"
                           :tel "tel"
                           :str "text"
                           :int "number"
                           :long-str nil
                           :select nil)
          input-element
          (case input-type
            :doc-ref
            (when read-only
              (let [doc-ref (firestore/deserialize-doc-ref value)
                    doc-id (:doc-id doc-ref)]
                [:input (merge input-attrs {:type raw-input-type :value doc-id})]))
            :long-str
            [textarea (merge input-state-attrs input-attrs)]
            [:input (merge input-attrs {:type raw-input-type})])
          datalist-element
          (when datalist
            [:datalist {:id datalist-uuid}
             (for [value datalist]
               ^{:key value} [:option {:value value}])])]
      [:<>
       (if clickable
         [views-core/href-link href input-element]
         input-element)
       datalist-element])))

(defn checkbox
  "A Reagent component for rendering a checkbox input.
   It uses the `select` component internally to represent the boolean state (true/false)
   as selectable options ('Taip'/'Ne').
   This provides a consistent look and feel with other select inputs while representing a simple boolean choice."
  [{:keys [state-cursor state-atom state-key read-only]}]
  (let [state-cursor (or state-cursor (r/cursor state-atom [state-key]))]
    [select {:state-cursor state-cursor
             :read-only read-only}
     [{:value true :label "Taip"}
      {:value false :label "Ne"}]]))

(defn labeled
  "A helper Reagent component to wrap child elements with a label.
   It takes a map of options including `:label` and an optional `:hide-set` of `state-key`s.
   If the component's `state-key` is in the `hide-set`, the component is not rendered.
   This is a common pattern for form elements to associate a descriptive label with an input field."
  [{:keys [label hide-set state-key]} & children]
  (when-not (contains? (set hide-set) state-key)
    (into
     [:div {:class "flex flex-col gap-1"}
      (when label
        [:label {:class "text-gray-700"} label])]
     children)))

(defn labeled-input
  "A helper Reagent component that combines `labeled` and `input`.
   It renders an input field wrapped with a label, using the same options map for both.
   This simplifies the rendering of standard labeled input fields in forms."
  [{:keys [hide-set state-key] :as options}]
  (when-not (contains? (set hide-set) state-key)
    [labeled options
     [input options]]))

(defn user-select
  "A Reagent component for selecting a user from a list.
   It uses the `select` component internally and fetches the list of active users
   from Firestore using subscriptions (`firestore/get-collection-subscription-atom`).
   In read-only mode, it fetches only the selected user's data.
   The component is not rendered if its `state-key` is in the `hide-set`.
   It's needed to provide a user-friendly way to select a user reference within a form."
  [{:keys [read-only state-atom state-key hide-set required]}]
  (r/with-let [is-editable (not read-only)
               selected-user-uid
               (r/reaction (get @state-atom state-key))
               users
               (r/reaction
                (if is-editable
                  @@(r/track
                     firestore/get-collection-subscription-atom
                     state/user-col-ref
                     [:where "active" "==" true])
                  (when-let [selected-user-uid @selected-user-uid]
                    (when-let [user
                               @@(r/track
                                  firestore/get-doc-subscription-atom
                                  (firestore/document-ref
                                   state/user-col-ref
                                   selected-user-uid))]
                      [user]))))]
    (when-not (contains? (set hide-set) state-key)
      [select {:read-only read-only
               :state-atom state-atom
               :state-key state-key
               :required required
               :prompt "Pasirinkite vartotoją"}
       (for [user @users]
         (let [uid (:uid user)
               label (views-core/get-user-label user)]
           {:value uid :label label}))])))

(defn patient-select
  "A Reagent component for selecting a patient from a list.
   It uses the `select` component internally and fetches the list of active patients
   from Firestore using subscriptions (`firestore/get-collection-subscription-atom`).
   The patient collection reference is derived from the implied user (`state/get-implied-user-patient-col-ref-atom`).
   In read-only mode, it fetches only the selected patient's data.
   Provides a link (`value-to-href-fn`) to the patient view when in read-only mode and a patient is selected.
   The component is not rendered if its `state-key` is in the `hide-set`.
   It's needed to provide a user-friendly way to select a patient reference within a form,
   considering the user context and providing navigation to the patient details."
  [{:keys [read-only state-atom state-key hide-set read-only-set required]}]
  (let
   [read-only (or read-only (contains? read-only-set state-key))
    is-editable (not read-only)
    selected-patient-uid
    (r/reaction (get @state-atom state-key))
    patients
    (if is-editable
      (r/reaction
       (when-let [patient-col-ref
                  @@(r/track
                     state/get-implied-user-patient-col-ref-atom)]
         @@(r/track
            firestore/get-collection-subscription-atom
            patient-col-ref
            [:where "active" "==" true])))
      (r/reaction
       (when-let [selected-patient-uid @selected-patient-uid]
         (when-let [patient-col-ref @@(r/track state/get-implied-user-patient-col-ref-atom)]
           (when-let [patient
                      @@(r/track
                         firestore/get-doc-subscription-atom
                         (firestore/document-ref
                          patient-col-ref
                          selected-patient-uid))]
             [patient])))))]
    (when-not (contains? (set hide-set) state-key)
      [select {:read-only read-only
               :state-atom state-atom
               :state-key state-key
               :required required
               :value-to-href-fn
               #(rfe/href :medplast.routes/patient-view
                          {:user @@(r/track state/get-implied-user-uid-atom)
                           :id %})
               :prompt "Pasirinkite pacientą"}
       (for [patient @patients]
         (let [value (firestore/get-doc-id patient)
               label (views-core/get-patient-label patient)]
           {:value value :label label}))])))

(defn submit-button
  "A simple Reagent component for rendering an HTML `<button>` styled as a submit button.
   Takes `:on-click` and `:disabled` options.
   Provides basic styling and disabled states."
  [{:keys [on-click disabled text]}]
  (let [class
        (str "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
             " disabled:bg-gray-400 disabled:cursor-not-allowed disabled:opacity-50")
        disabled (or disabled false)
        button-text (or text "Pateikti")]
    [:button
     {:type "submit"
      :class class
      :disabled disabled
      :on-click on-click}
     button-text]))

(defn- form-container
  "A simple Reagent component to wrap form elements within an HTML `<form>` and a container div.
   Provides basic layout styling for the form elements.
   Assumes the list of elements is static when using `into`."
  [& elements]
  [:div
   [:form {:class "max-w-md"}
    (into
      ; NOTE assumes that list is static, doesn't provide :key, uses into
     [:div {:class "grid grid-cols-1 gap-8"}]
     elements)]])

(defn- make-check-and-submit-callback
  "Creates an event handler function for form submission.
   This handler prevents the default form submission, checks the form's validity
   using the browser's built-in validation (`.checkValidity`), and either calls
   the provided `submit-fn` if the form is valid or the `invalid-fn` if it's invalid,
   also triggering the browser's validation reporting (`.reportValidity`).
   This is needed to integrate HTML5 form validation with the application's submission logic."
  [submit-fn invalid-fn]
  (fn [event]
    (.preventDefault event)
    (let [form (.. event -target -form)
          valid (.checkValidity form)]
      (if valid
        (submit-fn)
        (do
          (.reportValidity form)
          (invalid-fn))))))

(defn- form-submit-button
  "A Reagent component for a submit button that integrates with form validation.
   It uses the `submit-button` component and provides an `:on-click` handler
   created by `make-check-and-submit-callback`.
   This ensures that the form is validated before the `submit-fn` is called."
  [{:keys [submit-fn invalid-fn disabled text]}]
  [submit-button
   {:on-click (make-check-and-submit-callback submit-fn invalid-fn)
    :text text
    :disabled disabled}])

(defn- maybe-error-notification
  "A Reagent component to display error messages based on the state of an `error-state` atom.
   It checks for both a general `:error` (e.g., from an exception) and an `:invalid` input message.
   Displays the appropriate message with red text.
   Provides visual feedback to the user when form submission fails due to errors or invalid input."
  [error-state]
  (when-let [error-state @error-state]
    (let [error (:error error-state)
          invalid-input-msg (:invalid error-state)
          attrs {:class "text-red-500"}]
      [:div
       (when (some? error)
         (let [message (.-message error)
               code (.-code error)
               formatted (str (when code (str code " ")) message)]
           [:span attrs formatted]))
       (when invalid-input-msg
         [:span attrs invalid-input-msg])])))

(defn- error-and-submit-controls
  "A Reagent component that combines the error notification and the form submit button.
   It renders these controls only when the form is not in `read-only` mode.
   This groups the feedback and action elements commonly found at the bottom of a form."
  [{:keys [read-only error-state submit-fn invalid-fn submit-pending button-text]}]
  (when (not read-only)
    [:<>
     [maybe-error-notification error-state]
     [form-submit-button
      {:submit-fn submit-fn
       :invalid-fn invalid-fn
       :text button-text
       :disabled submit-pending}]]))

(defn form
  "The main Reagent component for rendering a complete form.
  
  It manages the form's state using a `state-atom`, handles submission, validation,
  and displays errors.

  ## Parameters:
  - `state-atom`: A Reagent atom that holds the current state of the form.
  - `read-only`: Optional boolean flag to render the form in a non-editable mode.
  - `<?valid-fn`: An async validation and submission function, required when the form is not read-only.

  ## Error System:
  The error system distinguishes between two kinds of errors:
  - **Validation Errors**: When the form is invalid (fails `FormHTMLNode.checkValidity()` check), 
    the `invalid-fn` sets a user-visible error message.
  - **Submission Errors**: If the async `<?valid-fn` throws an exception during submission, it is caught and displayed.

  ## Behavior:
  - When submission is attempted, `submit-pending-atom` prevents concurrent submissions.
  - If `<?valid-fn` is not provided and the form is not read-only, an assertion will fail.
  - On successful validation and submission, the error state is cleared and submission concludes.

  ## Component Structure:
  The component uses `r/with-let` to define two internal atoms:
  - `error-state`: Holds any errors related to submission or validation.
  - `submit-pending-atom`: Tracks whether a submission is currently in progress.

  The form contents are composed using the passed `elements`, wrapped in a `form-container`.
  It also includes `error-and-submit-controls`, which renders submission buttons and error messages."
  [{:keys [state-atom read-only <?valid-fn button-text]} & elements]
  (r/with-let [error-state (r/atom nil)
               submit-pending-atom (r/atom false)]
    (let [read-only (or read-only false)
          _ (when-not read-only (assert <?valid-fn "No <?valid-fn provided"))
          submit-fn
          #(go
             (if (not @submit-pending-atom)
               (try
                 (reset! submit-pending-atom true)
                 (<? (<?valid-fn state-atom))
                 (catch :default e
                   (js/console.error e)
                   (reset! error-state {:error e}))
                 (finally (reset! submit-pending-atom false)))
               (println "submission already pending!")))
          invalid-fn #(reset! error-state {:invalid "Ištaisykite anketoje klaidas."})]
      (->
       (into [form-container] elements)
       (conj
        [error-and-submit-controls
         {:read-only read-only
          :submit-pending @submit-pending-atom
          :error-state error-state
          :submit-fn submit-fn
          :invalid-fn invalid-fn
          :button-text button-text}])))))

(defprotocol PeriodProtocol
  "Protocol defining the behavior expected from a Period data structure.
   Requires implementations for checking if the period is fully defined (`defined?`)
   and if the period is valid (start date is less than or equal to end date) (`valid-period?`)."
  (defined? [_])
  (valid-period? [_]))

(defrecord Period [start-date end-date]
  PeriodProtocol
  (defined? [_]
    "Checks if both `start-date` and `end-date` fields of the Period record are non-nil.
    Returns true if both are present, false otherwise."
    (every? some? [start-date end-date]))
  (valid-period? [p]
    "Checks if the Period record represents a valid time period.
     A period is valid if it is defined (both start and end dates are present)
     and the `start-date` is less than or equal to the `end-date`.
     Returns true if valid, false otherwise."
    (and (defined? p)
         (<= start-date end-date))))

(defn make-period
  "Constructor function for creating a `Period` record.
   Takes `start-date` and `end-date` as arguments and returns a new `Period` instance.
   Provides a convenient way to create Period records."
  [start-date end-date] (Period. start-date end-date))

(defn period-selector
  "A Reagent component for selecting a date range (period).
   It uses two `labeled-input` components for the start and end dates,
   binding them to the `:start-date` and `:end-date` keys of the provided `period-atom` cursor.
   This component provides a dedicated UI for inputting time periods within a form."
  [period-atom]
  (let [end-date-atom (r/cursor period-atom [:end-date])
        start-date-atom (r/cursor period-atom [:start-date])]
    [:div {:class "max-w-md"}
     [:div {:class "grid grid-cols-1 gap-8"}
      [labeled-input
       {:label "Pradžios data"
        :input-type :date
        :state-cursor start-date-atom
        :input-attrs {:required true}}]
      [labeled-input
       {:label "Pabaigos data"
        :input-type :date
        :state-cursor end-date-atom
        :input-attrs {:required true}}]]]))

(defn watch-inactive-validity-constraint
  "Adds a watch to a `state-atom` to enforce a constraint: a document represented by the atom
   cannot be both `:invalid` and `:active` simultaneously.
   If the state becomes both invalid and active, it automatically sets `:active` to false.
   This watch is only active when the `read-only` flag is false (i.e., the form is editable).
   Returns a function to remove the watch.
   This constraint helps maintain data integrity by ensuring invalid documents are marked as inactive."
  [state-atom read-only]
  ; makes sure a document is not invalid and active at the same time
  ; only active when read-only flag is true
  ; returns a remove-watch function
  (let [k (random-uuid)
        should-watch (not read-only)]
    (when should-watch
      (add-watch
       state-atom k
       (fn [_key a _old new]
         (let [should-deactivate (and (:invalid new) (:active new))]
           (when should-deactivate
             (swap!
              a
              (fn [new]
                (assoc new :active false))))))))
    (fn []
      (when should-watch
        (remove-watch state-atom k)))))

(defn watch-inactive-constraint
  "Adds a watch to a `state-atom` to control the `:active` status based on other boolean keys.
   If any of the keys in `deactivating-bool-keys` are true in the atom's state,
   it sets `:active` to false. Otherwise, it sets `:active` to true.
   This watch is only active when the `read-only` flag is false (i.e., the form is editable).
   Returns a function to remove the watch.
   This constraint allows defining conditions based on form data that should automatically
   mark a document as inactive."
  [state-atom read-only deactivating-bool-keys]
  ; makes document inactive if any of deactivating-booleans are true
  ; otherwise makes it active
  (let [k (random-uuid)
        should-watch (not read-only)]
    (when should-watch
      (add-watch
       state-atom k
       (fn [_key a _old new]
         (let [active (:active new)
               a-deactivating-boolean-is-true
               (some (fn [bool-key] (get new bool-key)) deactivating-bool-keys)
               should-be-active (not a-deactivating-boolean-is-true)
               should-make-change (not= active should-be-active)]
           (when should-make-change
             (swap!
              a
              (fn [new]
                (assoc new :active should-be-active))))))))
    (fn []
      (when should-watch
        (remove-watch state-atom k)))))

(defn clarification
  "A simple Reagent component to display clarification text.
   Renders the text within a paragraph with specific styling to indicate it's a clarification."
  [text]
  [:p {:class "ml-0 italic opacity-60"} "(" text ")"])

(defn <?handle-valid-submit!
  "Handles the asynchronous submission of a valid form.
   Takes an async function `<?attempt-fn` which performs the actual submission logic
   and an `on-success` callback function to be executed upon successful completion of `<?attempt-fn`.
   Manages a `submission-in-progress` atom to prevent multiple submissions.
   Uses `clojure.core.async/go` and `medplast.lang/go-try` for asynchronous execution and error handling.
   Ensures the `on-finish` callback is always called after the submission attempt, regardless of success or failure.
   This function provides a standardized way to handle the final step of form submission after validation."
  [{:keys [<?attempt-fn on-success]}]
  (assert (not (nil? <?attempt-fn)))
  (assert (not (nil? on-success)))
  (r/with-let [submission-in-progress (r/atom false)]
    (when-not @submission-in-progress
      (reset! submission-in-progress true)
      (go
        (let [on-finish
              #(reset! submission-in-progress false)
              submission-c
              (go-try
               (<? (<?attempt-fn))
               (on-success))
              ; whether the submission result is an error or a value, it is forwarded
              result (<! submission-c)]
          ; on-finish is always run, irrespective of success or failure
          (on-finish)
          result)))))
