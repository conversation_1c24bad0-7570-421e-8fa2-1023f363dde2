(ns medplast.firestore
  (:require
   [env :as env]
   [clojure.string]
   [clojure.edn]
   [medplast.lang :refer [<?js-promise-to-chan] :refer-macros [go-try <?]]
   [medplast.firebase :as firebase]
   goog.dom
   goog.style
   ; ["firebase/app" :rename {initializeApp initialize-firebase-app} :as raw-firebase]
   ; ["firebase/auth" :as fb-auth]
   ["firebase/firestore" :as fs]
   ; ["firebase/functions" :as raw-functions]
   [cljs.core.async :as async :refer [<! go]]
   [cljs-bean.core :refer [->clj ->js]]
   [reagent.core :as r]))

(set! *warn-on-infer* true)

(defonce firestore-instance
  (let [db (fs/initializeFirestore
             firebase/firebase-app
             #js {:localCache (fs/persistentLocalCache
                                #js {:tabManager (fs/persistentMultipleTabManager)})})]
    (when env/dev
      (let [emulator-conf (env/get :firebase-emulator)
            host (:host emulator-conf)
            port (js/parseInt (:firestore-port emulator-conf))]
        (fs/connectFirestoreEmulator db host port)))
    db))

(defprotocol ColLookupProtocol
  "Protocol for looking up documents and collections."
  (get-doc-ref [this id] "Returns a DocRef for a document with the given ID within this collection.")
  (get-collection-atom [this] "Returns a Reagent atom containing a snapshot of the documents in this collection (non-reactive).")
  (get-collection-subscription-atom [this] "Returns a Reagent atom that is reactively updated with the documents in this collection as they change in Firestore."))

(defprotocol ColUpdateProtocol
  "Protocol for updating documents within a collection."
  (<?add-or-overwrite-doc! [this doc] "Adds a new document or overwrites an existing one with the provided data. Returns a channel.")
  (<?partial-update-doc! [this partial-doc] "Partially updates an existing document with the provided data. Returns a channel."))

(defprotocol DocLookupProtocol
  "Protocol for looking up a specific document."
  (doc-ref-to-col-id-to-doc-id-map [this] "Converts a DocRef into a map of collection ID to document ID segments representing its path.")
  (get-doc-subscription-atom [this] "Returns a Reagent atom that is reactively updated with the document's data as it changes in Firestore.")
  (get-doc-atom [this] "Returns a Reagent atom containing a snapshot of the document's data (non-reactive)."))

(defprotocol ColQueryableProtocol
  "Protocol for objects that can be queried like a Firestore collection (ColRef or ColGroupRef)."
  (reset-constraints [_ constraints] "Resets the query constraints for this queryable object.")
  (add-constraints [_ new-constraints] "Adds new constraints to the existing query constraints.")
  (add-pagination-constraints [_ pagination-params] "Adds pagination constraints (limit, start-after) to the query.")
  (to-col-query-js [_] "Converts the queryable object and its constraints into a Firestore JS Query object."))

(defn- parse-constraint
  "Parses a single constraint vector into a Firestore JS constraint object (where, orderBy, limit, etc.)."
  [constraint]
; NOTE jei reiks palaikymo keliem constraintam vienu metu,
; galima isplesti formata is
; [:order-by "stock-expiration-date" "asc"]
; i [[:order-by "stock-expiration-date" "asc"], [...], ...]
; ir taip bus backwards compatible, nes galima tikrinti pirmo elemento tipa.
  (cond
    (sequential? constraint)
    (let [[constraint-type & args] constraint
          args (map ->js args)]
      (assert (not (sequential? constraint-type)))
      (case constraint-type
        :where (apply fs/where args)
        :order-by (apply fs/orderBy args)
        :limit (apply fs/limit args)
        :start-after (apply fs/startAfter args)
        :start-at (apply fs/startAt args)))
    :else constraint))

(defn- get-col-ref-path
  "Extracts the slash-separated path string from a ColRef record."
  [col-ref]
  (let [path (clojure.string/join "/" (:path-segments col-ref))]
    ; TODO remove for prod
    ; check if doesn't result in double slashes
    (assert (not (re-matches #".*//.*" path)))
    path))

(defn- apply-constraints-to-query-js
  "Applies a list of constraints (in Clojure format) to a Firestore JS Query object.
  Constraints should look like this:
  [[:order-by \"stock-expiration-date\" \"asc\"], [...], ...]"
  [query-js constraints]
  ; constraints should look like this:
  ; [[:order-by "stock-expiration-date" "asc"], [...], ...]
  ; (println "constraints" constraints)
  (reduce
   (fn [query-js constraint]
     (if constraint
       (fs/query
        query-js
        (parse-constraint constraint))
       query-js))
   query-js
   constraints))

(extend-type fs/CollectionReference
  IPrintWithWriter
  (-pr-writer [obj writer _]
    ;; Replace this with the custom way you want to serialize
    (write-all writer (str "#<CollectionReference: " (.-path obj) ">"))))

(extend-type fs/DocumentReference
  IPrintWithWriter
  (-pr-writer [obj writer _]
    ;; Replace this with the custom way you want to serialize
    (write-all writer (str "#<DocumentReference: " (.-path obj) ">"))))

(defn- subscribe-to-query-snap
  "Subscribes to a Firestore JS Query snapshot changes.
  Returns a map containing a Reagent atom that updates with the raw snapshot
  and an unsubscribe function."
  [query-js]
  (let [query-snap-atom (r/atom [])
        handler
        (fn [snapshot]
          ; (println "snapshot received")
          (reset! query-snap-atom snapshot))
        unsubscribe-fn
        (fs/onSnapshot query-js handler)]
    {:query-snap-atom query-snap-atom
     :unsubscribe-fn unsubscribe-fn}))

(defn- get-path-from-doc-snap
  "Extracts the collection path segments (excluding the document ID) from a Firestore JS DocumentSnapshot."
  [doc-snap]
  ; doesn't include doc id
  (loop [path []
         ref (.-ref doc-snap)]
    (if (nil? ref)
      (rest path) ; discards first item, returns rest
      (let [id (.-id ref)
            parent (.-parent ref)]
        (recur
         (conj path id)
         parent)))))

(defn get-doc-path
  "Retrieves the path segments associated with a document map (added by `doc-snap-to-doc`)."
  [doc]
  (::doc-path doc))

(defn set-doc-path
  "Associates a path (vector of strings) with a document map."
  [doc path]
  (assoc doc ::doc-path path))

(defn get-doc-id
  "Retrieves the document ID associated with a document map (added by `doc-snap-to-doc`)."
  [doc]
  (::doc-id doc))

(defn remove-doc-path
  "Removes the path information from a document map."
  [doc]
  (dissoc doc ::doc-path))

(defn set-doc-id
  "Associates a document ID with a document map."
  [doc id]
  (assoc doc ::doc-id id))

(defn remove-doc-id
  "Removes the document ID information from a document map."
  [doc]
  (dissoc doc ::doc-id))

(defn- doc-snap-to-doc
  "Converts a Firestore JS DocumentSnapshot to a Clojure map,
  including the document data, ID, and path."
  [doc-snap]
  (when (and (some? doc-snap) (.exists doc-snap))
    (let [id (.-id doc-snap)
          js-data (.data doc-snap)
          data (->clj js-data)
          path (get-path-from-doc-snap doc-snap)]
      (-> data
          (set-doc-id id)
          (set-doc-path path)))))

(defn- query-snap-to-docs
  "Converts a Firestore JS QuerySnapshot to a sequence of Clojure document maps."
  [query-snap]
  (->>
   query-snap
   .-docs
   (map doc-snap-to-doc)))

(defn subscribe-to-docs
  "Subscribes to real-time updates for a collection reference (ColRef or ColGroupRef).
  Returns a map containing a Reagent atom that is reactively updated with the documents
  and an unsubscribe function."
  [col-ref]
  (let [query-js (to-col-query-js col-ref)
        {:keys [query-snap-atom unsubscribe-fn]}
        (subscribe-to-query-snap query-js)
        docs-atom
        (r/reaction
         (query-snap-to-docs @query-snap-atom))
        ; uuid (random-uuid)
        unsubscribe-fn
        (fn []
          ; (println "unsubscribe(to-docs)" uuid col-ref)
          (unsubscribe-fn))
       ;_ (add-watch docs-atom :watcher1
       ;             (fn [_key _ref _old new]
       ;               #_(println "subscribe-to-docs" uuid col-ref
       ;                          "\nold=new" (= old new)
       ;                          "\nold" old
       ;                          "\nnew" new)))
        ]
    ; (println "subscribe-to-docs" uuid col-ref)
    ; (js/console.debug "subscribe-to-docs query-js" query-js)
    {:docs-atom docs-atom
     :unsubscribe-fn unsubscribe-fn}))

(defn- serialize-doc
  "Prepares a Clojure document map for writing to Firestore by removing internal keys
  like `::doc-path` and `::doc-id` and converting it to a JS object."
  [doc]
  (->
   doc
   remove-doc-path
   remove-doc-id
   ->js))

(defn- <?add-doc-to-coll!
  "Adds a new document with an auto-generated ID to a collection.
  Returns a channel that yields the DocumentReference of the newly created document."
  [^ColRef col-ref doc]
  (let [col-ref-js (to-col-query-js col-ref)]
    (->>
     (serialize-doc doc)
     (fs/addDoc col-ref-js)
     <?js-promise-to-chan)))

(defn- to-doc-ref-js
  "Converts a Clojure DocRef record to a Firestore JS DocumentReference object."
  [{:keys [col-ref doc-id] :as _doc-ref}]
  (let [col-path (get-col-ref-path col-ref)]
    (fs/doc firestore-instance col-path doc-id)))

(defn- <?create-or-overwrite-doc!
  "Creates a new document or overwrites an existing one at a specific DocumentReference.
  Returns a channel that yields when the operation is complete."
  [^DocRef doc-ref doc]
  (let [doc-ref-js (to-doc-ref-js doc-ref)]
    (->>
     (serialize-doc doc)
     (fs/setDoc doc-ref-js)
     <?js-promise-to-chan)))

#_(defn <?delete-doc! [^DocRef doc-ref]
    (let [doc-ref-js (to-doc-ref-js doc-ref)]
      (go-try
       (->>
        (fs/deleteDoc doc-ref-js)
        <?js-promise-to-chan
        <?))))

; marks field for deletion when used in an updateDoc as the value for a field
; https://firebase.google.com/docs/firestore/manage-data/delete-data#fields
; NOTE not sure if calling this here and reusing it is safe
(def
  ^{:doc "A special value used in `<?partial-update-doc!` to mark a field for deletion."}
  field-deletion-mark (fs/deleteField))

(defn register-for-unsubscription
  "A helper function for Reagent components to manage Firestore subscriptions.
  It takes a function `f` that returns a map containing an `:unsubscribe-fn`
  and one or more atoms (e.g., `:docs-atom` or `:doc-atom`).
  It registers the unsubscribe function to be called when the component is unmounted
  and returns the atom(s) for use in the component's render function."
  [f & args]
  (r/with-let [unsubscribe-fns-atom (atom ())
               ; uuid (random-uuid)
               ]
    ; (println "register-for-unsubscription" uuid)
    (let [{:keys [unsubscribe-fn] :as out}
          (apply f args)
          vs (-> out (dissoc :unsubscribe-fn) vals)
          ; making sure data structure is as expected
          ; this is a more flexible alternative to just doing :doc(s)-atom
          _ (assert (= 1 (count vs)))
          v (first vs)]
      (swap! unsubscribe-fns-atom conj unsubscribe-fn)
        ; there used to be a bug here.
        ; it's this implicit deref in the commented out println
        ; here that causes reevaluation loops upon mutation.
        ; (println v) implicitly derefs v to display its current value
        ; the deref is detected and when its value changes causes the tracked
        ; function to rerun.
        ; could it be that this is actually desirable, in case a different atom
        ; is derefed and the output of this function depends on it?
        ; (println "register-for-unsubscription v" uuid v)
      v)
    (finally
      ; (println "register-for-unsubscription finally" uuid)
      (doseq [unsubscribe-fn @unsubscribe-fns-atom]
        (unsubscribe-fn)))))

(defn- make-snapshot-handler
  "Creates a snapshot handler function for a single document subscription.
  This handler updates the provided `doc-atom` with the latest document data
  converted to a Clojure map."
  [doc-atom]
  (fn [snapshot]
    (let [doc (doc-snap-to-doc snapshot)]
      (reset! doc-atom doc))))

#_(defn side-effect-after [n-seconds side-effect-fn]
    (js/setTimeout side-effect-fn (* n-seconds 1000)))

(defn- subscribe-to-doc
  "Subscribes to real-time updates for a specific document reference.
  Returns a map containing a Reagent atom that is reactively updated with the document data
  and an unsubscribe function."
  [doc-ref]
  (let [default nil
        doc-atom (r/atom default)
        ; uuid (random-uuid)
        ; ; TODO remove
        ; _ (add-watch
        ;    doc-atom :watcher1
        ;    (fn [_key _ref old new]
        ;      (println "subscribe-to-doc doc-atom watch" uuid doc-ref
        ;                         ; "\nold=new" (= old new)
        ;               "\nold" old
        ;               "\nnew" new)))
        doc-ref-js (to-doc-ref-js doc-ref)
        unsubscribe-fn
        (fs/onSnapshot
         doc-ref-js
         (make-snapshot-handler doc-atom)
         ; #(->> % doc-snap-to-doc (reset! doc-atom))
         )
        ; ; NOTE this doesn't have an effect on call loop
        ; _ (side-effect-after 1 #(reset! doc-atom "112233"))
        ; _ (side-effect-after 2 #(reset! doc-atom "112233 2"))
        unsubscribe-fn
        (fn []
          ; (println "unsubscribe(to-doc)" uuid doc-ref)
          ; TODO remove
          (remove-watch doc-atom :watcher1)
          (unsubscribe-fn))]
    ; (println "subscribe-to-doc before return" uuid doc-ref)
    {:doc-atom doc-atom
     :unsubscribe-fn unsubscribe-fn}))

(defrecord DocRef [col-ref doc-id]
  DocLookupProtocol
  (doc-ref-to-col-id-to-doc-id-map [_doc-ref]
    ; returns a col-id -> doc-id map
    (let [col-ref-path (:path-segments col-ref)
          path (conj col-ref-path doc-id)
          in-pairs (partition 2 path)
          ; NOTE (mapv vec) necessary for conversion to {} to work
          hm (->> in-pairs (mapv vec) (into {}))]
      hm))
  (get-doc-subscription-atom [doc-ref]
    (register-for-unsubscription subscribe-to-doc doc-ref)
    ; (:doc-atom (subscribe-to-doc doc-ref))
    )
  (get-doc-atom [doc-ref]
    (let [a (r/atom nil)]
      (go
        (let [c
              (go-try
               (let [doc-ref-js (to-doc-ref-js doc-ref)
                     js-promise (fs/getDoc doc-ref-js)
                     doc-snap (<? (<?js-promise-to-chan js-promise))
                     doc (doc-snap-to-doc doc-snap)]
                 doc))
              doc (<? c)]
          (reset! a doc)))
      a)))

(defn document-ref
  "Creates a DocumentReference record for a document within a collection.
  Returns nil if either `col-ref` or `id` is nil."
  [^ColRef col-ref id]
  (when (and col-ref id)
    (DocRef. col-ref id)))

(defn- <?add-or-overwrite-doc!-private
  "Internal function to add or overwrite a document.
  If the document map includes an `::doc-id`, it overwrites the document with that ID.
  Otherwise, it adds a new document with an auto-generated ID.
  Returns a go block channel that yields the result of the operation."
  [^ColRef col-ref doc]
  (go-try
   (let [doc-id (get-doc-id doc)
         doc-ref (document-ref col-ref doc-id)
         c (if doc-ref
              ; NOTE promises don't resolve while offline
             (<?create-or-overwrite-doc! doc-ref doc)
             (<?add-doc-to-coll! col-ref doc))
         result (<? c)]
     result)))

(defn- <?partial-update-doc!-private
  "Internal function to partially update a document at a specific DocumentReference.
  Returns a channel that yields when the operation is complete."
  [^DocRef doc-ref partial-doc]
  (let [doc-ref-js (to-doc-ref-js doc-ref)]
    (->>
     (serialize-doc partial-doc)
     (fs/updateDoc doc-ref-js)
     <?js-promise-to-chan)))

(defn- <?get-collection-c
  "Fetches a snapshot of documents for a collection queryable object (ColRef or ColGroupRef).
  Returns a go block channel that yields a sequence of document maps."
  [col-queryable]
  (go-try
   (let [col-query-js (to-col-query-js col-queryable)
         js-promise (fs/getDocs col-query-js)
         query-snap (<? (<?js-promise-to-chan js-promise))
         docs (query-snap-to-docs query-snap)]
     docs)))

(defn- get-collection-atom-private
  "Returns a Reagent atom containing a snapshot of the documents in a collection queryable object.
  This atom is populated once and does not update reactively to changes in Firestore."
  [col-queryable]
  (let [a (r/atom nil)]
    (go
      (let [c (<?get-collection-c col-queryable)]
        (reset! a (<! c))))
    a))

(defn single-constraint?
  "Checks if the given value represents a single constraint vector (e.g., `[:where \"field\" \">\" 10]`)."
  [constraint]
  ; a single constraint will always look like this:
  ; [:xyz ... .... ...]
  (and (sequential? constraint) (keyword? (first constraint))))

(defn make-sure-single-constraint-is-wrapped
  "Ensures that a single constraint vector is wrapped in a collection,
  so it can be correctly processed as a list of constraints."
  [constraints]
  (if (single-constraint? constraints)
    [constraints]
    constraints))

(defn- add-pagination-constraints-to-col-ref
  "Adds pagination constraints (limit and start-after) to a collection reference."
  [col-ref {:keys [n-limit start-after-value] :as _pagination-params}]
  ; (assert (instance? PaginationParams pagination-params))
  (add-constraints
   col-ref
   [(when n-limit
      [:limit n-limit])
    (when start-after-value
      [:start-after start-after-value])]))

#_(defrecord PaginationParams [n-limit start-after-value])

(defrecord ColRef [path-segments constraints]
  ColQueryableProtocol
  (add-constraints [_ new-constraints]
    (ColRef.
     path-segments
     (concat
      constraints
      (make-sure-single-constraint-is-wrapped new-constraints))))
  (reset-constraints [_ constraints]
    (ColRef. path-segments constraints))
  (add-pagination-constraints [this pagination-params]
    (add-pagination-constraints-to-col-ref this pagination-params))
  (to-col-query-js [col-ref]
    (let [col-ref-js-path (get-col-ref-path col-ref)
          col-ref-js (fs/collection firestore-instance col-ref-js-path)]
      (apply-constraints-to-query-js col-ref-js constraints)))
  ColLookupProtocol
  (get-doc-ref [col-ref id]
    (DocRef. col-ref id))
  (get-collection-atom [col-ref]
    (get-collection-atom-private col-ref))
  (get-collection-subscription-atom [col-ref]
    (register-for-unsubscription subscribe-to-docs col-ref))
  ColUpdateProtocol
  (<?add-or-overwrite-doc! [col-ref doc]
    (<?add-or-overwrite-doc!-private col-ref doc))
  (<?partial-update-doc! [dbe partial-doc]
    (let [doc-id (get-doc-id partial-doc)
          doc-ref (get-doc-ref dbe doc-id)]
      (assert (some? doc-id))
      (<?partial-update-doc!-private doc-ref partial-doc))))

(defrecord ColGroupRef [col-id constraints]
  ColQueryableProtocol
  (add-constraints [_ new-constraints]
    (ColGroupRef.
     col-id
     (concat
      constraints
      (make-sure-single-constraint-is-wrapped new-constraints))))
  (reset-constraints [_ new-constraints]
    (ColGroupRef. col-id new-constraints))
  (add-pagination-constraints [this pagination-params]
    (add-pagination-constraints-to-col-ref this pagination-params))
  (to-col-query-js [_]
    (let [col-group-ref-js (fs/collectionGroup firestore-instance col-id)]
      (apply-constraints-to-query-js col-group-ref-js constraints)))
  ColLookupProtocol
  (get-doc-ref [_col-ref _id]
    (throw (ex-info "Not implemented" {} :not-implemented)))
  (get-collection-atom [col-ref]
    (get-collection-atom-private col-ref))
  (get-collection-subscription-atom [col-ref]
    (register-for-unsubscription subscribe-to-docs col-ref)))

(defn- correct-constraints
  "Ensures that constraints are in the correct format (a collection of constraint vectors),
  wrapping a single constraint vector if necessary."
  [constraints]
  ; wraps a single unwrapped constraint
  ; [:where x y z] -> [[:where x y z]]
  ; doesn't affect constraints like [[:where x y z]]
  (when constraints
    (let [single-constraint-unwrapped
          (not (coll? (first constraints)))]
      (if single-constraint-unwrapped
        [constraints]
        constraints))))

(defn collection-ref
  "Creates a CollectionReference record representing a Firestore collection at a specific path.
  `path-segments` is a vector or list of strings representing the path (e.g., `[\"users\" user-id \"orders\"]`).
  `constraints` is an optional collection of query constraints (e.g., `[[:where \"status\" \"==\" \"pending\"]]`)."
  ([path-segments constraints]
   (when (every? some? path-segments)
     (assert (or (list? path-segments) (vector? path-segments)))
     (let [constraints (correct-constraints constraints)
           col-ref (ColRef. path-segments constraints)]
       col-ref)))
  ([path-segments]
   (collection-ref path-segments nil)))

(defn collection-group-ref
  "Creates a CollectionGroupReference record representing a query across all collections
  with the same ID, regardless of their position in the Firestore hierarchy.
  `id` is the string ID of the collection group.
  `constraints` is an optional collection of query constraints."
  ([id constraints]
   (when (string? id)
     (let [constraints (correct-constraints constraints)]
       (ColGroupRef. id constraints))))
  ([id]
   (collection-group-ref id nil)))

(defn serialize-doc-ref
  "Serializes a DocRef record into a string representation using `pr-str`.
  This is useful for storing DocRefs, e.g., in local storage or as document fields."
  [doc-ref] (pr-str doc-ref))

#_(defn pr-inter [x] (println "x" x) x)

(defn deserialize-doc-ref
  "Deserializes a string representation of a DocRef (created by `serialize-doc-ref`)
  back into a Clojure DocRef record."
  [s]
  (clojure.edn/read-string
   {:readers {'medplast.firestore.DocRef map->DocRef
              'medplast.firestore.ColRef map->ColRef
              'medplast.firestore.ColGroupRef map->ColGroupRef}}
   s))
