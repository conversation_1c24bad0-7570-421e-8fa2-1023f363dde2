/*
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    function fieldExists(field) {
      let data = request.resource.data;
      return field in data;
    }

    function isStringField(field) {
      let data = request.resource.data;
      let value = data[field];
      return fieldExists(field) && 
             value is string;
    }

    function isNonEmptyStringField(field) {
      let data = request.resource.data;
      let value = data[field];
      return isStringField(field) &&
             value.size() > 0;
    }

    function isPositiveIntegerField(field) {
      let data = request.resource.data;
      let value = data[field];
      return fieldExists(field) && 
             value is int && 
             value > 0;
    }

    function isNonNegativeIntegerField(field) {
      let data = request.resource.data;
      let value = data[field];
      return fieldExists(field) && 
             value is int && 
             value >= 0;
    }

    function whenExistsIsPositiveInteger(field) {
      return !fieldExists(field) || 
        isPositiveIntegerField(field);
    }

    function whenExistsIsNonNegativeInteger(field) {
      return !fieldExists(field) || 
        isNonNegativeIntegerField(field);
    }

    function whenExistsIsStringField(field) {
      return !fieldExists(field) || 
        isStringField(field);
    }

    function productCorrect() {
      return isNonEmptyStringField("name") &&
        whenExistsIsStringField("variant-name") &&
        whenExistsIsStringField("description") &&
        isPositiveIntegerField("quantity-per-package") &&
        whenExistsIsNonNegativeInteger("quantity-compensated-per-month");
    }

    match /{document=**} {
      allow write, update, read: if true;
    }

    match /product/{productId} {
      allow create, update: if productCorrect();
    }

    match /sale/{saleId} {
      allow create, update: if true;
    }
  }
}
*/
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // deny by default; probably redundant;
    // presumes that at least one rule must allow a request for it to succeed
    /*
    match /{document=**} {
      allow write, read: if false;
    }
    */
    
    function isAuthenticated() {
      return request.auth != null;
    }

    function isRequestingUser(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() && 'admin' in request.auth.token && request.auth.token.admin == true;
    }

    function notFrozen() {
      // seconds since epoch
      let lastDeactivatedDefined = 'lastDeactivated' in request.resource.data;

      // milliseconds since epoch
      let nowMs = request.time.toMillis();
      // seconds since epoch
      let now = nowMs;

      let oneSecond = 1 * 1000;
      let oneMinute = oneSecond * 60;
      let oneDay = oneMinute * 60 * 24;
      let oneWeek = oneDay * 7;
      // in seconds
      // let timeToFreeze = oneWeek * 6;
      // TODO CRITICAL PROD: Change this back to 6 weeks
      let timeToFreeze = oneSecond * 10;

      let frozen = lastDeactivatedDefined && ((request.resource.data.lastDeactivated + timeToFreeze) < now);
      return !frozen;
    }

    // NOTE: A read rule can be broken into get and list,
    // while a write rule can be broken into create, update, and delete

    match /log/{docId} {
      allow read: if isAdmin();
    }

    match /product/{docId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    match /user/{authId} {
      allow read: if isAuthenticated();
      allow create, update: if isRequestingUser(authId) || isAdmin();

      match /patient/{saleId} {
        allow read, create, update: if isRequestingUser(authId) || isAdmin();
      }
      
      match /sale/{saleId} {
        allow read, create: if isRequestingUser(authId) || isAdmin();
        allow update: if (isRequestingUser(authId) && notFrozen()) || isAdmin();
      }
    }
      
    match /{path=**}/sale/{saleId} {
      allow read: if isAdmin();
    }

    match /{path=**}/patient/{saleId} {
      allow read: if isAdmin();
    }
  }
}

// vim: ft=javascript
