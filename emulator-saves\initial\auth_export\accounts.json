{"kind": "identitytoolkit#DownloadAccountResponse", "users": [{"localId": "6etAaNnTi9OpK542LTwGdbrLj4Fm", "lastLoginAt": "*************", "displayName": "user1", "photoUrl": "", "emailVerified": false, "email": "<EMAIL>", "salt": "fakeSaltnCnYJl2uYFlCyMJgtiHj", "passwordHash": "fakeHash:salt=fakeSaltnCnYJl2uYFlCyMJgtiHj:password=password", "passwordUpdatedAt": *************, "validSince": "**********", "mfaInfo": [], "createdAt": "*************", "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "user1", "photoUrl": ""}], "customAttributes": "{\"admin\":true}"}, {"localId": "x062LwQDjAPSJVuMpu5ahyBlh6Ec", "lastLoginAt": "**********279", "displayName": "user2", "photoUrl": "", "emailVerified": false, "email": "<EMAIL>", "salt": "fakeSalt94Hvzoni3b7BXAXRLxO3", "passwordHash": "fakeHash:salt=fakeSalt94Hvzoni3b7BXAXRLxO3:password=password", "passwordUpdatedAt": **********279, "validSince": "**********", "mfaInfo": [], "createdAt": "**********279", "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "user2", "photoUrl": ""}]}]}