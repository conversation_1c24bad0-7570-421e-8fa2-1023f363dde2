// This file is meant to hold common functions and utilities that are used across multiple cloud functions. It shouldn't hold actual cloud function exports.

const admin = require("firebase-admin");

admin.initializeApp(); // This might be redundant if initializeApp is called elsewhere (e.g. index.js or userManagement.js)
// However, common.js itself uses admin.firestore() and admin.auth() so it might need its own initialization if used standalone or tested.
// Given the project structure, initializeApp() is likely called once at the top level (e.g. in index.js implicitly or explicitly).
// For safety in this refactoring, I'll assume admin is initialized once globally. If issues arise, this might need revisiting.
// Let's stick to the original common.js structure for admin.initializeApp() and db.
const db = admin.firestore();

const https = require("firebase-functions/v2/https");
const { getAuth } = require("firebase-admin/auth");

// Kept helper functions
function getObjectValue(obj, key, defult) {
  if (obj && key in obj) {
    return obj[key];
  } else {
    return defult;
  }
}

function removeNullOrUndefined(obj) {
  const map = new Map(Object.entries(obj));
  for (let [key, value] of map) {
    if (value === null || value === undefined) {
      map.delete(key);
    }
  }
  const newObj = Object.fromEntries(map);
  return newObj;
}

async function callerMustBeLoggedIn(request) {
  if (!request) {
    throw new https.HttpsError(
      "invalid-argument",
      "Request object is required."
    );
  }
  const callerUid = request.auth?.uid;
  if (!callerUid) {
    throw new https.HttpsError(
      "unauthenticated",
      "User must be logged in to call this function."
    );
  }
  const userRecord = await getAuth().getUser(callerUid);
  const isActive = userRecord['disabled'] !== true;
  if (!isActive) {
    throw new https.HttpsError(
      "permission-denied",
      "Only active users can call this."
    );
  }
}

async function callerMustBeAdmin(request) {
  await callerMustBeLoggedIn(request);
  const callerUid = request.auth.uid;
  const userRecord = await getAuth().getUser(callerUid);
  const customClaims = userRecord.customClaims;
  const isAdmin = customClaims["admin"] === true;
  if (!isAdmin) {
    throw new https.HttpsError(
      "permission-denied",
      "Only administrators can call this.",
    );
  }
}

function validateNonEmptyStrings(...args) {
  for (const arg of args) {
    if (typeof arg !== "string" || arg.trim() === "") {
      throw new https.HttpsError("invalid-argument", "An argument failed test of being a non-empty string.");
    }
  }
}

function markDocDataAsAssignedToUserUid(docData, userUid) {
  docData['assigned-to'] = userUid;
}

async function userDocExists(userId) {
  const userDoc = await db.collection('user').doc(userId).get();
  return userDoc.exists;
}

async function validateTargetUser(sourceUserId, targetUserId) {
  if (sourceUserId === targetUserId) {
    throw new https.HttpsError("invalid-argument", "Source and target users cannot be identical.");
  }
  const targetExists = await userDocExists(targetUserId);
  if (!targetExists) {
    throw new https.HttpsError("invalid-argument", "Target user does not exist.");
  }
}

const regionOpts = {region: "europe-west1"};

// Exports
exports.callerMustBeAdmin = callerMustBeAdmin;
exports.validateNonEmptyStrings = validateNonEmptyStrings;
exports.regionOpts = regionOpts;
exports.getObjectValue = getObjectValue;
exports.removeNullOrUndefined = removeNullOrUndefined;

exports.movePatient = https.onCall(
  regionOpts,
  async (request) => {
  await callerMustBeAdmin(request);
  const sourceUserId = request.data['from-user'] || request.data.fromUser;
  const targetUserId = request.data['to-user'] || request.data.toUser;
  const patientId = request.data.patient;
  validateNonEmptyStrings(sourceUserId, targetUserId, patientId);
  await validateTargetUser(sourceUserId, targetUserId);
  const sourcePatientPath = `/user/${sourceUserId}/patient/${patientId}`;
  const targetPatientPath = `/user/${targetUserId}/patient/${patientId}`;
  const saleCollectionPath = `/user/${sourceUserId}/sale`;
  const targetSaleCollectionPath = `/user/${targetUserId}/sale`;
  const patientDocRef = db.doc(sourcePatientPath);
  try {
    await db.runTransaction(async (transaction) => {
      const patientDoc = await transaction.get(patientDocRef);
      if (!patientDoc.exists) {
          throw new https.HttpsError("invalid-argument", `Patient document with ID ${patientId} does not exist.`);
      }
      const salesQuery = db.collection(saleCollectionPath)
                           .where('patient', '==', patientId);
      const salesSnapshot = await transaction.get(salesQuery);
      salesSnapshot.forEach((saleDoc) => {
          const saleData = saleDoc.data();
          markDocDataAsAssignedToUserUid(saleData, targetUserId);
          const targetSaleDocRef = db.doc(`${targetSaleCollectionPath}/${saleDoc.id}`);
          transaction.set(targetSaleDocRef, saleData);
          transaction.delete(saleDoc.ref);
      });
      const patientData = patientDoc.data();
      markDocDataAsAssignedToUserUid(patientData, targetUserId);
      transaction.set(db.doc(targetPatientPath), patientData);
      transaction.delete(patientDocRef);
    });
    console.log(`Successfully moved patient ${patientId} and associated sales to user ${targetUserId}`);
  } catch (error) {
    console.error(`Failed to move patient ${patientId} and associated sales:`, error);
    throw error;
  }
});

const axios = require("axios");
const { defineSecret } = require('firebase-functions/params');
const docusealApiKeyParam = defineSecret('DOCUSEAL_API_KEY');

function validateDocusealUrl(apiUrl) {
  if (!apiUrl) {
    throw new https.HttpsError(
      "invalid-argument",
      "The 'url' parameter is required."
    );
  }
  if (!apiUrl.startsWith("https://api.docuseal.eu/")) {
    throw new https.HttpsError(
      "invalid-argument",
      "The URL must start with 'https://api.docuseal.eu/'."
    );
  }
}

async function makeDocusealRequest(apiUrl, body, apiKey) {
  const response = await axios.get(apiUrl, {
    params: body,
    headers: { 'X-Auth-Token': apiKey },
  });
  return response.data;
}

exports.docusealProxy = https.onCall(
  {
    region: regionOpts.region,
    secrets: [docusealApiKeyParam]
  },
  async (request) => {
    await callerMustBeLoggedIn(request);
    const apiUrl = request.data.url;
    const body = request.data.body || null;
    validateDocusealUrl(apiUrl);
    try {
      const docusealApiKey = "yTkD6S98vyakr3UTcdCvGPmvqLrizBBkKmoaBmYwFX3" // TODO CRITICAL PROD: Use docusealApiKeyParam.value()
      const responseData = await makeDocusealRequest(apiUrl, body, docusealApiKey);
      return { data: responseData };
    } catch (error) {
      console.error("Error making proxy request:", error);
      throw new https.HttpsError(
        "internal",
        "Failed to fetch data from the other API."
      );
    }
  }
);/**
 * Reflects user data from Firebase Auth to Firestore, merging with existing data.
 * If there's a conflict, Firebase Auth data wins.
 * @param {string|null} signingInUserUid - UID of the user currently signing in (if any),
 *                                         or a specific user UID to ensure is updated.
 *                                         Only to be set when triggered by this user signing in.
 *                                         His sign-in time will be recorded.
 * @returns {Promise<void>}
 */

async function _reflectUsers(signingInUserUid) {
  try {
    const allUsers = await _listAllUsers(signingInUserUid);
    const userCollection = db.collection("user");
    const batch = db.batch();
    Object.keys(allUsers).forEach((uid) => {
      const docRef = userCollection.doc(uid);
      const userDoc = allUsers[uid];
      // Update existing user docs in firestore by merging
      batch.set(docRef, userDoc, { merge: true });
    });
    await batch.commit();
    console.log("Successfully reflected users to Firestore.");
  } catch (error) {
    console.error("Error in reflectUsers function:", error); 
    throw new Error("Failed to execute reflectUsers function");
  }
}
exports._reflectUsers = _reflectUsers;

/**
 * Get all users from Firebase Authentication
 * @param {string|null} signingInUserUid - If triggered by sign-in, UID of the user currently signing in
 * @returns {Promise<Object>} Object mapping user UIDs to user data
 */
async function _listAllUsers(signingInUserUid) {
  return await _fetchUserBatch(signingInUserUid, null);
}

/**
 * Fetch a batch of users from Firebase Auth
 * @param {string|null} signingInUserUid - UID of the user currently signing in (if any)
 * @param {string|null} nextPageToken - Token for pagination
 * @returns {Promise<Object>} Object mapping user UIDs to user data
 */
async function _fetchUserBatch(signingInUserUid, nextPageToken) {
  const batchSize = 1000;
  try {
    const listUsersPromise = nextPageToken === null
      ? admin.auth().listUsers(batchSize)
      : admin.auth().listUsers(batchSize, nextPageToken);
    const listUsersResult = await listUsersPromise;
    const users = {};
    listUsersResult.users.forEach((userRecord) => {
      const transformedUser = _transformUserRecord(userRecord, signingInUserUid);
      users[userRecord.uid] = transformedUser;
    });
    if (listUsersResult.pageToken) {
      const nextUsers = await _fetchUserBatch(signingInUserUid, listUsersResult.pageToken);
      return { ...users, ...nextUsers };
    }
    return users;
  } catch (error) {
    console.error("Error fetching user batch:", error);
    throw error;
  }
}

/**
 * Transform a Firebase Auth user record into a simplified user object
 * @param {Object} userRecord - The Firebase Auth user record
 * @param {string|null} signingInUserUid - UID of the user currently signing in (if any)
 * @returns {Object} Transformed user object with nulls removed
 */
function _transformUserRecord(userRecord, signingInUserUid) {
  const uid = userRecord.uid;
  const email = userRecord.email;
  let displayName = userRecord.displayName;
  if (displayName === "") {
    displayName = null;
  }
  let phoneNumber = userRecord.phoneNumber;
  if (phoneNumber === "") {
    phoneNumber = null;
  }
  let lastSignInTime = userRecord.metadata.lastSignInTime;
  if (signingInUserUid && uid === signingInUserUid) {
    const date = new Date();
    const formattedDate = date.toUTCString();
    lastSignInTime = formattedDate;
  }
  const creationTime = userRecord.metadata.creationTime;
  const claims = userRecord.customClaims;
  const isAdmin = getObjectValue(claims, "admin", false);
  const disabled = getObjectValue(userRecord, "disabled", false);
  const active = !disabled;
  const mfaEnrolledFactors = getObjectValue(getObjectValue(userRecord, 'multiFactor'), 'enrolledFactors', []);
  const mfaEnrolledFactorsTransformed = Array.isArray(mfaEnrolledFactors)
    ? mfaEnrolledFactors.map(factor => removeNullOrUndefined({
      "display-name": factor.displayName ?? null,
      "enrollment-time": factor.enrollmentTime,
      "factor-id": factor.factorId,
      "uid": factor.uid,
      "phone-number": factor.phoneNumber ?? null,
    }))
    : undefined;
  const userWithNulls = {
    uid: uid,
    email: email,
    "display-name": displayName,
    "last-sign-in-time": lastSignInTime,
    "creation-time": creationTime,
    "phone-number": phoneNumber,
    admin: isAdmin,
    active: active,
    "mfa-enrolled-factors": mfaEnrolledFactorsTransformed,
  };
  return removeNullOrUndefined(userWithNulls);
}

