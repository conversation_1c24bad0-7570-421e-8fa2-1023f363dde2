(ns medplast.views.grids.product
  (:require
   [medplast.state :as state]
      ; [medplast.firebase :as firebase]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]
   [medplast.firestore :as firestore]))

(defn make-action-cell-renderer [user]
  (grids-core/make-action-cell-renderer
   user
   (fn [doc]
     (let [doc-id (firestore/get-doc-id doc)]
       [{:href (rfe/href :medplast.routes/product-view {:id doc-id})
         :text "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
        {:href (rfe/href :medplast.routes/product-edit {:id doc-id})
         :admin-only true
         :text "Redaguoti"}]))))

(defn make-col-defs [user]
  [{:field "actions"
    :headerName "Veiksmai"
    :cellRenderer (make-action-cell-renderer user)
    :width 180
    :sortable false
    :autoHeight true
    :wrapText true}
   {:field "type" :headerName "Rūšis" :width 150
    :autoHeight true
    :wrapText true
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}}
   {:field "name" :headerName "Pavadinimas"
    :flex 0.15 :minWidth 150
    :maxWidth 250
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}
    :autoHeight true :wrapText true}
   {:field "variant-name" :headerName "Varianto pavad."
    :flex 0.3 :minWidth 250
    :maxWidth 350
    :autoHeight true
    :wrapText true
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "description"
    :headerName "Aprašymas"
    :flex 1 :minWidth 200
    :maxWidth 600
    :cellRenderer grids-core/compacted-long-text-cell-renderer
    :filter true
    :filterParams #js {:filteroptions #js ["contains"]}
    :autoHeight true
    :wrapText true}
   {:field "quantity-per-package" :headerName "Kiekis pakuotėje"
    :width 120
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "quantity-compensated-per-period" :headerName "Kiek kompensuojamas per laikotarpį"
    :width 120
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "compensation-period-in-months" :headerName "Kompensacijos laikotarpis (mėn.)"
    :width 120
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "addition-date" :headerName "Sukūrimo data"
    :width 120
    :autoHeaderHeight true
    :wrapHeaderText true}
   {:field "active" :headerName "Aktyvus?"
    :cellRenderer grids-core/boolean-cell-renderer
    :width 150}])

(def initial-column-config
  [{:colId "active", :sort "desc", :sortIndex 0}
   {:colId "type", :sort "asc", :sortIndex 1}
   {:colId "name", :sort "asc", :sortIndex 2}
   {:colId "variant-name", :sort "asc", :sortIndex 3}])

(defn products-table []
  (r/with-let [row-data-atom
               @(r/track
                 firestore/get-collection-subscription-atom
                 state/product-col-ref)
               user-atom @(r/track state/get-logged-in-user-atom)]
    [table {:row-data-atom row-data-atom
            :col-defs (make-col-defs @user-atom)
            :initial-column-config initial-column-config}]))

(defn product-list-page []
  [:<>
   [header "Produktai"
    {:on-click views-core/go-back! :text "Atgal"}
    {:href (rfe/href :medplast.routes/product-add)
     :admin-only true
     :text "Naujas produktas"}]
   [products-table]])
