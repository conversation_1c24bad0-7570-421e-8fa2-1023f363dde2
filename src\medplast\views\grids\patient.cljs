(ns medplast.views.grids.patient
  (:require
   [clojure.string]
   [medplast.lang :as lang]
   [medplast.state :as state]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   #_[cljs.pprint :rename {pprint println}]
   [reitit.frontend.easy :as rfe]
))

(defn make-action-cell-renderer [user]
  (grids-core/make-action-cell-renderer
   user
   (fn [doc]
     (let [doc-id (firestore/get-doc-id doc)
           user-uid (views-core/get-user-uid-from-patient doc)]
       [{:href (views-core/get-href-for-patient doc)
         :text "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
        {:href (rfe/href :medplast.routes/patient-edit
                         {:user user-uid :id doc-id})
         :text "Redaguoti"}]))))

(defn dynamic-list-to-string [kw-ix-to-item-map]
  (->> kw-ix-to-item-map
       (lang/sort-map-keys-by lang/keywordized-int-to-int)
       vals
       (clojure.string/join "\n")))

#_(defn dynamic-input-list-cell-renderer [params]
    (let [doc (grids-core/get-row-data params)
          field-str (.. params -colDef -field)
          field-kw (keyword field-str)
          ix-to-item-map (get doc field-kw)
          first-ix (->>
                    ix-to-item-map
                    keys
                    (sort-by lang/keywordized-int-to-int)
                    first)
          primary-item (get ix-to-item-map first-ix)]
      (r/as-element
       [:span primary-item])))

(defn make-col-defs [user]
  (->> [{:field "actions"
         :headerName "Veiksmai"
         :cellRenderer (make-action-cell-renderer user)
         :width 180
         :sortable false
         :autoHeight false
         :wrapText true}
        (when (-> user :admin)
          {:field "salesperson"
           :headerName "Vartotojas"
           :cellRenderer grids-core/user-cell-renderer
           :valueFormatter grids-core/user-value-formatter
           :autoHeight false
           :wrapText false
           :filter true
           :filterParams #js {:filteroptions #js ["contains"]
                              :textFormatter grids-core/user-text-formatter}})
        {:field "last-name" :headerName "Pavardė"
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "first-name" :headerName "Vardas"
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "contacts" :headerName "Kontaktai"
         :autoHeight true
         :wrapText true
         :cellRenderer grids-core/compacted-long-text-cell-renderer
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "addresses" :headerName "Adresai"
         :autoHeight true
         :wrapText true
         :cellRenderer grids-core/compacted-long-text-cell-renderer
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "stoma-type" :headerName "Stomos tipas"
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "pharmacy" :headerName "Vaistinė"
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "comments" :headerName "Komentarai" :width 400
         :cellRenderer grids-core/compacted-long-text-cell-renderer
         :autoHeight true
         :wrapText true
         :filter true
         :filterParams #js {:filteroptions #js ["contains"]}}
        {:field "addition-date" :headerName "Sukūrimo data" :width 150
         :filter true}
        {:field "signed" :headerName "Pasirašė?" :width 150
         :cellRenderer grids-core/boolean-cell-renderer
         :cellClassRules grids-core/active-cell-class-rules
         :autoHeaderHeight true
         :wrapHeaderText true :filter true}
        {:field "active" :headerName "Aktyvus?" :width 150
         :cellRenderer grids-core/boolean-cell-renderer
         :cellClassRules grids-core/active-cell-class-rules
         :autoHeaderHeight true
         :wrapHeaderText true :filter true}
        {:field "invalid" :headerName "Klaidingas?" :width 150
         :cellRenderer grids-core/boolean-cell-renderer
         :cellClassRules grids-core/invalid-cell-class-rules
         :autoHeaderHeight true
         :wrapHeaderText true :filter true}]
       (filter some?)))

(def initial-column-config
  [{:colId "active", :sort "desc", :sortIndex 0}
   {:colId "last-name", :sort "asc", :sortIndex 1}
   {:colId "first-name", :sort "asc", :sortIndex 2}])

(defn get-salesperson-by-patient [id-to-user-map patient]
  (let [doc-path (firestore/get-doc-path patient)
        user-id (second doc-path)]
    (get id-to-user-map user-id)))

(defn process-table-data [id-to-user-map row-data]
  (for [patient row-data]
    (-> patient
        (update :addresses dynamic-list-to-string)
        (update :contacts dynamic-list-to-string)
        (assoc :salesperson
               (get-salesperson-by-patient id-to-user-map patient)))))

(defn patient-table []
  (r/with-let [user-atom @(r/track state/get-logged-in-user-atom)
               row-data-atom @(r/track state/get-visible-patients-atom)
               id-to-user-map-atom @(r/track state/get-id-to-user-map-atom)
               row-data-atom
               (r/reaction
                (process-table-data @id-to-user-map-atom @row-data-atom))]
    [table {:row-data-atom row-data-atom
            :col-defs (make-col-defs @user-atom)
            :initial-column-config initial-column-config}]))

(defn patient-list-page []
  (let [user-uid @@(r/track state/get-implied-user-uid-atom)]
    [:<>
     [header "Pacientai"
      {:on-click views-core/go-back!
       :text "Atgal"}

      {:href (rfe/href :medplast.routes/patient-add {:user user-uid})
       :text "Naujas pacientas"}]
     [patient-table]]))

(defn user-patients-table [user-id]
  ; TODO fix so keeps updated when user-id changes
  (let [user-atom @(r/track state/get-user-atom user-id)
        row-data-atom @(r/track state/get-user-patients-atom user-id)
        id-to-user-map-atom @(r/track state/get-id-to-user-map-atom)
        row-data-atom
        (r/reaction
         (process-table-data @id-to-user-map-atom @row-data-atom))]
    [table {:row-data-atom row-data-atom
            :col-defs (make-col-defs @user-atom)
            :initial-column-config initial-column-config}]))
