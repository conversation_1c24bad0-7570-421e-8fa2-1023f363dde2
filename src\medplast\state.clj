(ns medplast.state)

#_(defmacro extend-types
  [types protocol & body]
  (let [extend-call (fn [t] `(extend-type ~t ~protocol ~@body))]
    `(do ~@(map extend-call types))))

#_(defmacro pass-through-when-atom-ready [f dbe-a & params]
  ;`(let [dbe# @~dbe-a]
     `(if (waiting-or-nil? @~dbe-a)
       ~dbe-a
       (apply ~f ~params)))

#_(defmacro extend-type-nil [protocol]
  (let [methods (keys (ns-publics (symbol (str (ns-name *ns*)) (str protocol))))
        method-impls (map (fn [m]
                            `(~m [~'this# ~'& args#] nil))
                          methods)]
    `(extend-type nil
       ~protocol
       ~@method-impls)))
