(ns medplast.views.grids.sign
  (:require
   [medplast.lang :refer-macros [go-try <?]]
   [cljs-bean.core :refer [->clj]]
   [clojure.string]
   [medplast.firebase :as firebase]
   [medplast.cloud-fn :as cloud-fn]
   [medplast.firestore :as firestore]
   [medplast.state :as state] ; [medplast.data :as data]
   [medplast.views.core :as views-core]
   [medplast.views.forms.core :as forms-core :refer [checkbox form labeled labeled-input]]
   [medplast.views.forms.patient :refer [dynamic-input-list]]
   [medplast.views.grids.core :as grids-core]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]))

(defn <?get-templates []
  (let [limit 20
        query (str "?" "limit=" limit)
        url (str "https://api.docuseal.eu/templates" query)
        body nil]
    (go-try
     (let [js-out (<? (cloud-fn/<?call-docuseal-proxy url body))]
       ; (println js-out)
       (->clj js-out)))))

(defn get-templates-atom-for-patient-id [patient-id]
  (r/with-let [templates-atom (r/atom nil)
               last-patient-id (r/atom nil)]
    ; only get when patient-id changed, effectively caching the atom value
    (when (not= @last-patient-id patient-id)
      (reset! last-patient-id patient-id)
      (go-try
       (let [templates (<? (<?get-templates))
             templates (-> templates :data :data :data)]
         ; (println templates)
         (reset! templates-atom templates))))
    templates-atom))

(defn <?get-submitters [patient-id]
  (let [limit 20
        query (str "?external_id=" patient-id "&limit=" limit)
        url (str "https://api.docuseal.eu/submitters" query)
        body nil]
    (go-try
     (let [js-out (<? (cloud-fn/<?call-docuseal-proxy url body))]
       ; (println js-out)
       (->clj js-out)))))

(defn process-submitter [submitter]
  (let [template (:template submitter)
        template-name (:name template)
        template-id (:id template)
        started-at (:created_at submitter)
        document-urls (map :url (:documents submitter))
        completed-at (:completed_at submitter)]
    {:template-name template-name
     :template-id template-id
     :document-urls document-urls
     :started-at started-at
     :completed-at completed-at}))

(defn get-submissions-atom-for-patient-id [patient-id]
  (r/with-let [submissions-atom (r/atom nil)
               last-patient-id (r/atom nil)]
    ; only get when patient-id changed, effectively caching the atom value
    (when (not= @last-patient-id patient-id)
      (reset! last-patient-id patient-id)
      (go-try
       (let [submitters (<? (<?get-submitters patient-id))
             submitters (-> submitters :data :data :data)
             submissions (map process-submitter submitters)]
         ; (println submitters)
         (reset! submissions-atom submissions))))
    submissions-atom))

(defn template-list []
  [:<>
   [views-core/header "Dokumentai kuriuos galima pasirašyti"
    {:on-click views-core/go-back! :text "Atgal"}]
   (let [patient-id @state/route-match-doc-id-atom
         templates-atom
         @(r/track get-templates-atom-for-patient-id patient-id)
         initial-column-config
         [{:colId "created_at", :sort "asc", :sortIndex 0}
          ;{:colId "last-name", :sort "asc", :sortIndex 1}
          ;{:colId "first-name", :sort "asc", :sortIndex 2}
          ]
         action-cell-renderer
         (grids-core/make-action-cell-renderer
          nil ;user
          (fn [template]
            (let [slug (:slug template)
                  patient-id @state/route-match-doc-id-atom
                  user-uid @state/route-match-user-uid-atom]
              [{:href (rfe/href
                       :medplast.routes/patient-sign
                       {:user user-uid :id patient-id :form-template-slug slug})
                :text "Pasirašyti"}])))
         col-defs
         [{:field "actions"
           :headerName "Veiksmai"
           :cellRenderer action-cell-renderer
           :width 100
           :sortable false
           :autoHeight true
           :wrapText true}
          {:field "name" :headerName "Pavadinimas"
           :filter true
           :filterParams #js {:filteroptions #js ["contains"]}}
          #_{:field "folder_name" :headerName "Aplankas"
             :filter true
             :filterParams #js {:filteroptions #js ["contains"]}}
          {:field "id" :headerName "id"
           :filter true
           :filterParams #js {:filteroptions #js ["contains"]}}
          {:field "created_at" :headerName "Sukūrimo data" :width 300
           :filter true}
          {:field "updated_at" :headerName "Atnaujinimo data" :width 300
           :filter true}]]
     [grids-core/table {:row-data-atom templates-atom
                        :col-defs col-defs
                        :initial-column-config initial-column-config}])])

(defn signed-list []
  [:<>
   [views-core/header "Pasirašyti dokumentai"
    {:on-click views-core/go-back! :text "Atgal"}]
   (let [patient-id @state/route-match-doc-id-atom
         submissions-atom
         @(r/track get-submissions-atom-for-patient-id patient-id)
         initial-column-config
         [{:colId "completed-at", :sort "asc", :sortIndex 0}
          ;{:colId "last-name", :sort "asc", :sortIndex 1}
          ;{:colId "first-name", :sort "asc", :sortIndex 2}
          ]
         doc-link-cell-renderer
         (fn [params]
           (let [doc-links (grids-core/get-field-value params)]
             (r/as-element
              [:<>
               (for [doc-link doc-links]
                 (let [link-label (or (last (clojure.string/split doc-link "/")) doc-link)]
                   ^{:key doc-link}
                   [:div {:class "flex flex-col"}
                    [views-core/href-link doc-link link-label]]))])))
         col-defs
         [#_{:field "actions"
             :headerName "Veiksmai"
             :cellRenderer action-cell-renderer
             :width 100
             :sortable false
             :autoHeight true
             :wrapText true}
          {:field "template-name" :headerName "Pavadinimas"
           :filter true
           :filterParams #js {:filteroptions #js ["contains"]}}
          {:field "template-id" :headerName "id"
           :filter true
           :filterParams #js {:filteroptions #js ["contains"]}}
          {:field "document-urls" :headerName "Dokumentai"
           :cellRenderer doc-link-cell-renderer
           :autoHeight true
           :wrapText true
           :filter false
           :filterParams #js {:filteroptions #js ["contains"]}}
          {:field "started-at" :headerName "Atidarymo data" :width 300
           :filter true}
          {:field "completed-at" :headerName "Pasirašymo data" :width 300
           :filter true}]]
     [grids-core/table {:row-data-atom submissions-atom
                        :col-defs col-defs
                        :initial-column-config initial-column-config}])])

(defn <?set-signed! [patient-col-ref patient-id signed-atom]
  (let [partial-doc (firestore/set-doc-id {:signed @signed-atom} patient-id)]
    (firestore/<?partial-update-doc! patient-col-ref partial-doc)))

(defn signer-info []
  (r/with-let [patient-col-ref-atom @(r/track state/get-implied-user-patient-col-ref-atom)
               patient-id-atom state/route-match-doc-id-atom
               patient-atom
               (r/reaction
                (when-let [patient-col-ref @patient-col-ref-atom]
                  (when-let [patient-id @patient-id-atom]
                    @@(r/track firestore/get-doc-atom
                               (firestore/document-ref patient-col-ref patient-id)))))]
    (let [state-atom (r/atom @patient-atom)
          signed-cursor (r/cursor state-atom [:signed])
          ; NOTE referring to patient-atom, because dereffing state-atom would effectively unset it
          doc-available (some? @patient-atom)
          <?handle-valid-submit!
          ; TODO move fancy handle-valid-submit logic to form itself, pass its args to form directly
          (fn [] (forms-core/<?handle-valid-submit!
                  {:<?attempt-fn
                   #(<?set-signed!
                     @patient-col-ref-atom
                     @patient-id-atom
                     signed-cursor)
                   :on-success #()}))]
      ; (println "state-atom" state-atom)
      ; (println "signed-cursor" signed-cursor)
      [:<>
       [views-core/header "Pasirašantis pacientas"
        {:on-click views-core/go-back! :text "Atgal"}]
       (when doc-available
         [form
          {:state-atom state-atom :read-only false :<?valid-fn <?handle-valid-submit!}
          [labeled-input
           {:label "Vardas"
            :state-key :first-name
            :input-type :str
            :state-atom state-atom
            :read-only true}]
          [labeled-input
           {:label "Pavardė"
            :state-key :last-name
            :input-type :str
            :state-atom state-atom
            :read-only true}]
          [labeled {:label "Kontaktai"}
           [dynamic-input-list
            {:state-cursor (r/cursor state-atom [:contacts])
             :read-only true}]]
          [labeled {:label "Adresai"}
           [dynamic-input-list
            {:state-cursor (r/cursor state-atom [:addresses])
             :read-only true}]]
          [labeled {:label "Pasirašė?"}
           [:<>
            [checkbox
             {:state-cursor signed-cursor
              :read-only false}]]]])])))

(defn sign-list-page []
  [:<>
   [signer-info]
   [signed-list]
   [template-list]])
