(ns medplast.views.forms.user
  (:require
   [clojure.string]
   [clojure.set :refer [difference]]
   [medplast.views.forms.core :refer [form input labeled checkbox]]
   [medplast.views.grids.sale :as grids-sale]
   [medplast.views.grids.patient :as grids-patient]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.state :as state]
   [medplast.firebase :as firebase]
   [medplast.cloud-fn :as cloud-fn]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [medplast.lang :refer-macros [go-try <?]]
   [reitit.frontend.easy :as rfe]))


(defn- <?add-or-overwrite-user!
  "Asynchronously adds or overwrites a user document in the 'users' Firestore collection.
   It takes a user document map and uses the global user collection reference to perform the add/overwrite operation.
   This function is used for saving user data to the database."
  [doc]
  (firestore/<?add-or-overwrite-doc! state/user-col-ref doc))

(defn- <?handle-valid-submit!
  "Handles the submission of a valid user form.
   It asynchronously adds or overwrites the user document in Firestore using `<?add-or-overwrite-user!` with the current form state.
   Upon successful submission, it navigates back using `views-core/go-back!`.
   This function connects the user form's valid state to the action of saving the user data."
  [form-state-atom]
  (go-try
   (<? (<?add-or-overwrite-user! @form-state-atom))
   (views-core/go-back!)))

(defn- mfa-factors-display
  "Displays the list of enrolled MFA factors using forms-core/input components.
   Takes a cursor to the sequence of enrolled factors and a read-only flag."
  [enrolled-factors-cursor _read-only]
  (if (empty? @enrolled-factors-cursor)
    [:p "Antras faktorius neįtrauktas."]
    [:<>
     (doall
      (for [i (range (count @enrolled-factors-cursor))]
        (let [factor-cursor (r/cursor enrolled-factors-cursor [i])]
          ^{:key (:uid @factor-cursor)}
          [:<>
           #_([labeled {:label "Tipas"}
               [input
                {:input-type :str
                 :read-only read-only
                 :state-cursor (r/cursor factor-cursor [:factor-id])}]]
              [labeled {:label "Pavadinimas"}
               [input
                {:input-type :str
                 :read-only read-only
                 :state-cursor (r/cursor factor-cursor [:display-name])}]])
           [labeled {:label "Faktoriaus telefono numeris"}
            [input
             {:input-type :tel
              :read-only true
              :state-cursor (r/cursor factor-cursor [:phone-number])}]]
           [labeled {:label "Faktoriaus įtraukimo data"}
            [input
             {:input-type :datetime-local
              :read-only true
              :state-cursor (r/cursor factor-cursor [:enrollment-time])}]]])))]))

(defn- user-form
  "A Reagent component for rendering the user form.
   It uses the generic `form` component and includes various labeled inputs and checkboxes for user details.
   It displays read-only information about the user's email, phone number, display name, creation time, last sign-in time, active status, and admin status.
   It also includes a section to display Multi-Factor Authentication (MFA) enrollment information.
   This component provides the structure and fields for viewing and editing user information (though some fields are read-only here)."
  [{:keys [state-atom read-only]}]
  [form
   {:state-atom state-atom
    :read-only read-only
    :<?valid-fn <?handle-valid-submit!}
   [labeled {:label "El. paštas"}
    [input
     {:state-key :email
      :input-type :str
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Telefono numeris"}
    [input
     {:state-key :phone-number
      :input-type :tel
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Suteiktas vardas"}
    [input
     {:state-key :display-name
      :input-type :str
      :state-atom state-atom
      :read-only true
      :required true}]]
   [labeled {:label "Sukūrimo data"}
    [input
     {:state-key :creation-time
      :input-type :datetime-local
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Paskutinė įsiregistravimo data"}
    [input
     {:state-key :last-sign-in-time
      :input-type :datetime-local
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Aktyvus"}
    [checkbox
     {:state-key :active
      :state-atom state-atom
      :read-only true}]]
   [labeled {:label "Administratorius"}
    [checkbox
     {:state-key :admin
      :state-atom state-atom
      :read-only true}]]

   #_[views-core/header "Antro faktoriaus autentifikavimas"]
   #_[mfa-factors-display (r/cursor state-atom [:mfa-enrolled-factors]) read-only]])

(defn- switch-activity!
  "Switches the active status of a user in Firebase Authentication.
   It takes a user document map, extracts the UID and current active status, and calls the Firebase helper to set the active status to the opposite value.
   This function is used to activate or deactivate a user account.
   It displays any errors in an alert box and logs them to the console."
  [user-doc]
  (let [uid (:uid user-doc)
        active (:active user-doc)
        new-status (not active)
        action-text (if new-status "aktivuoti" "deaktyvuoti")]
    (medplast.lang/go-try
     (try
       (medplast.lang/<? (cloud-fn/<?set-user-activity! uid new-status))
       (catch :default e
         (js/console.error "Error switching user activity:" e)
         (js/alert (str "Nepavyko " action-text " vartotojo: " (.-message e))))))))

(defn- formulate-activation-link
  "Creates a map of attributes for a link that switches a user's active status.
   It takes a user document map and returns a map with `:on-click` and `:text` keys.
   The `:on-click` action is set to call `switch-activity!` with the user document.
   The `:text` is dynamically set to 'Deaktivuoti' or 'Aktivuoti' based on the user's current active status.
   This function is used to generate the link displayed in the user view and edit pages."
  [user-doc]
  (when user-doc
    {:on-click
     (partial switch-activity! user-doc)
     :text
     (if (:active user-doc)
       "Deaktivuoti"
       "Aktivuoti")}))

(defn user-view-page
  "The Reagent component for the user view page.
   It fetches user data based on the route parameter ID using a Firestore subscription.
   It displays the user information using the `user-form` in read-only mode.
   Includes a header with a back button, an 'Redaguoti' (Edit) link (admin only), and a dynamically generated activation/deactivation link.
   Conditionally renders an 'Atjungti antrą faktorių' (Unenroll MFA) button for admins viewing other users with MFA enrolled.
   Also displays tables of the user's patients and sales.
   This page is needed to display the details and related data of a specific user."
  [_match]
  (r/with-let [viewed-user-id-atom state/route-match-doc-id-atom
               viewed-user-atom
               @(r/track
                 firestore/get-doc-subscription-atom
                 (firestore/document-ref
                  state/user-col-ref
                  @viewed-user-id-atom))
               viewed-user-atom
               (r/reaction (views-core/process-user @viewed-user-atom))]
    (let [viewed-user-id @viewed-user-id-atom
          viewed-user @viewed-user-atom]
      [:<>
       [header "Peržiūrėti vartotoją"
        {:on-click views-core/go-back!
         :text "Atgal"}
        {:href (rfe/href :medplast.routes/user-edit {:id viewed-user-id})
         :admin-only true
         :text "Redaguoti"}
        ; NOTE hiding the activation link because this feels more like an operation like those exposed in the edit page.
        #_(formulate-activation-link viewed-user)]
       (if @viewed-user-atom
         [:<>
          [user-form {:state-atom viewed-user-atom :read-only true}]
          [header "Vartotojo pacientai"]
          [grids-patient/user-patients-table viewed-user-id]
          [header "Vartotojo pardavimai"]
          [grids-sale/user-sales-table viewed-user-id]]
         [:span "Kraunama arba nerasta..."])])))

(defn- set-of-pairs-to-hm
  "Converts a set of key-value pairs into a hash map."
  [set-of-pairs]
  (->> set-of-pairs (apply concat) (apply hash-map)))

(defn- find-the-update
  "Compares an old state map with a new state map and returns a map representing the changes.
   Includes keys that were removed from the old state (with a nil value) and keys with updated values in the new state.
   This function is used in the user edit page to determine which fields have been modified by the user."
  [old-state new-state]
  (let [removed-keys (difference (set (keys old-state)) (set (keys new-state)))
        niled-removals (->
                        (map (fn [k] [k nil]) removed-keys)
                        set-of-pairs-to-hm)]
    (->
     (difference (set new-state) (set old-state))
     set-of-pairs-to-hm
     (merge niled-removals))))

(defn user-edit-page
  "The Reagent component for the user edit page. Doesn't offer regular editing. Instead, delegates to mini-pages accessible via header links.
   It fetches user data based on the route parameter ID using a Firestore subscription.
   "
  [_match]
  (r/with-let [id-atom state/route-match-doc-id-atom
               doc-atom-atom (r/track
                              firestore/get-doc-subscription-atom
                              (firestore/document-ref
                               state/user-col-ref @id-atom))
               doc-atom (r/reaction (views-core/process-user @@doc-atom-atom))
               ;;doc-atom-mutable (r/atom nil)
               ; below watcher and update-finding logic attempts to both, feed
               ; back updates (e.g. when user is de/activated) and to allow
               ; editing the input fields without it being overwritten by
               ; irrelevant changes upstream.
               ;;  _ (add-watch
               ;;     doc-atom :updater
               ;;     (fn [_key _reference old-state new-state]
               ;;       (let [doc-mutable @doc-atom-mutable]
               ;;         (if doc-mutable
               ;;           (let [the-update (find-the-update old-state new-state)]
               ;;             (swap! doc-atom-mutable merge the-update))
               ;;           (reset! doc-atom-mutable new-state)))))
               ]
    (let [id @id-atom
          doc @doc-atom]
      [:<>
       [header "Redaguoti vartotoją"
        {:on-click views-core/go-back! :text "Atgal"}
        {:href (rfe/href :medplast.routes/user-view {:id id}) :text "Peržiūrėti"}
        (formulate-activation-link @doc-atom)
        {:href (rfe/href :medplast.routes/user-display-name-edit {:user id})
         :admin-only true
         :danger false
         :text "Keisti suteiktą vardą"}
        {:href (rfe/href :medplast.routes/user-email-edit {:user id})
         :admin-only true
         :danger true
         :text "Keisti el. paštą"}
        {:href (rfe/href :medplast.routes/user-phone-edit {:user id})
         :admin-only true
         :danger true
         :text "Keisti tel. numerį"}
        {:href (rfe/href :medplast.routes/user-password-reset {:user id})
         :admin-only true
         :danger true
         :text "Pakeisti slaptažodį"}]
       (case doc ; Use original doc state for case check
         ::firestore/waiting [:span "Kraunama..."] ; Use correct waiting keyword
         nil [:span "Nerasta."]
         ;; Pass mutable state and the simple submit handler
         [user-form {:state-atom doc-atom
                     :read-only true ; Disallow any editing, editing to be done via header links and dedicated mini-pages
                     ;; :<?valid-fn <?handle-valid-submit!
                     }])])))

;; --- Email/Phone Editing Pages ---

(defn- make-<?handle-user-attribute-update!
  "Handles the valid submission for updating a user attribute (email or phone).
   Takes the current state atom, the key of the attribute to update,
   the Firebase function to call for the update, and an error message string."
  [attribute-key firebase-update-fn]
  (fn <?handle-user-attribute-update! [state-atom]
    (go-try
     (let [user-uid @state/route-match-user-uid-atom
           new-value (attribute-key @state-atom)]
       (when-not (and user-uid new-value)
         (throw (js/Error. "Missing data for update.")))
       (<? (firebase-update-fn user-uid new-value))
       (views-core/go-back!)))))

(defn header-for-auth-attribute-edit
  "Creates a header for the user attribute edit page.
   Takes the attribute name and the user's display name."
  [text]
  (r/with-let [user-atom
               (r/reaction
                (when-let [user-uid @state/route-match-user-uid-atom]
                  @@(r/track state/get-user-atom user-uid)))]
    (let [user-label (views-core/get-user-label @user-atom)]
      [header [:span text " (" user-label ")"]
       {:on-click views-core/go-back! :text "Atgal"}])))

(defn log-out-warning []
  [:p "Pastaba: po šios operacijos vartotojas bus vienos valandos bėgyje atjungtas ir turės prisijungti iš naujo."])

(defn user-email-edit-page
  "Reagent component for the user email edit page."
  []
  (r/with-let [state-atom (r/atom nil)]
    [:<>
     [header-for-auth-attribute-edit "Keisti vartotojo el. paštą"]
     [log-out-warning]
     [form
      {:state-atom state-atom
       :<?valid-fn (make-<?handle-user-attribute-update! :email cloud-fn/<?admin-change-user-email!)}
      [labeled {:label "Naujas el. paštas"}
       [input
        {:state-key :email
         :input-type :email
         :state-atom state-atom
         :required true}]]]]))

(defn user-phone-edit-page
  "Reagent component for the user phone number edit page."
  []
  (r/with-let [state-atom (r/atom nil)]
    [:<>
     [header-for-auth-attribute-edit "Keisti vartotojo tel. numerį"]
     [log-out-warning]
     [form
      {:state-atom state-atom
       :<?valid-fn (make-<?handle-user-attribute-update! :phone-number cloud-fn/<?admin-change-user-phone-number!)}
      [labeled {:label "Naujas telefono numeris (E.164 formatu)"}
       [input
        {:state-key :phone-number
         :input-type :tel
         :state-atom state-atom
         :required true}]]]]))

(defn user-display-name-edit-page
  "Reagent component for the user display name edit page."
  []
  (r/with-let [state-atom (r/atom nil)]
    [:<>
     [header-for-auth-attribute-edit "Keisti vartotojui suteiktą vardą"]
     [form
      {:state-atom state-atom
       :<?valid-fn (make-<?handle-user-attribute-update! :display-name cloud-fn/<?admin-change-user-display-name!)}
      [labeled {:label "Naujas vardas"}
       [input
        {:state-key :display-name
         :input-type :str
         :state-atom state-atom
         :required true}]]]]))

(defn- make-<?handle-user-password-reset!
  "Handles the valid submission for resetting a user's password.
   Calls the Firebase function and stores the temporary password."
  [temp-password-display-atom]
  (fn <?handle-user-password-reset! [_state-atom] ; state-atom might not be needed if form has no inputs
    (go-try
     (let [user-uid @state/route-match-user-uid-atom]
       (when-not user-uid
         (throw (js/Error. "User ID not found for password reset.")))
       (let [result (<? (cloud-fn/<?admin-reset-user-temporary-password! user-uid))]
         (println "<?handle-user-password-reset! result" result)
         (if (:success result)
           (reset! temp-password-display-atom (:temporaryPassword result))
           (throw (js/Error. (or (:message result) "Failed to reset password.")))))))))

(defn- temporary-password-display
  "Component to display the temporary password after it has been generated."
  [temp-password]
  [:div
   [:h4 "Laikinas slaptažodis:"]
   [:p {:style {:font-weight "bold" :color "green"}} temp-password]
   [:p "Nukopijuokite šį slaptažodį ir perduokite vartotojui. Slaptažodis nebus rodomas dar kartą."]])

(defn- own-password-change-form
  "Form component for a user to change their own password."
  [form-state-atom]
  (let [handle-own-password-change
        (fn [state-atom]
          (go-try
           (let [new-password (:new-password @state-atom)]
             (when-not new-password
               (throw (js/Error. "Naujas slaptažodis neįvestas.")))
             (let [result (<? (cloud-fn/<?user-change-own-password! new-password))]
               (println "User change own password result:" result)
               (if (:success result)
                 (do
                   (js/alert "Slaptažodis sėkmingai pakeistas. Jums reikės prisijungti iš naujo.")
                   (firebase/<logout-user!))
                 (throw (js/Error. (or (:message result) "Nepavyko pakeisti slaptažodžio."))))))))]
    [:<>
     [:p "Čia galite pakeisti savo slaptažodį. Po pakeitimo jums reikės prisijungti iš naujo."]
     [form
      {:state-atom form-state-atom
       :button-text "Pakeisti slaptažodį"
       :<?valid-fn handle-own-password-change}
      [labeled {:label "Naujas slaptažodis"}
       [input
        {:state-key :new-password
         :input-type :password
         :state-atom form-state-atom
         :required true}]]]]))

(defn- admin-password-reset-form
  "Form component for an admin to reset another user's password."
  [form-state-atom temp-password-atom]
  [:<>
   [log-out-warning]
   [:p "Paspaudus mygtuką žemiau, vartotojo slaptažodis bus pakeistas į laikiną. Prisijungus, vartotojas turės sukurti naują slaptažodį."]
   [form
    {:state-atom form-state-atom
     :button-text "Pakeisti slaptažodį"
     :<?valid-fn (make-<?handle-user-password-reset! temp-password-atom)}]])

(defn- render-password-reset-content
  "Renders the appropriate content based on the current state."
  [temp-password-atom is-own-password form-state-atom]
  (cond
    ;; If temporary password has been generated, show it
    @temp-password-atom
    [temporary-password-display @temp-password-atom]

    ;; If user is changing their own password
    is-own-password
    [own-password-change-form form-state-atom]

    ;; Admin resetting someone else's password
    :else
    [admin-password-reset-form form-state-atom temp-password-atom]))

(defn user-password-reset-page
  "Reagent component for the user password reset page.
   If the current user is the same as the user being edited, allows direct password change.
   Otherwise (for admins), allows resetting to a temporary password."
  []
  (r/with-let [temp-password-atom (r/atom nil)
               form-state-atom (r/atom {})
               current-user-atom (r/track state/get-logged-in-user-atom)
               target-user-uid-atom state/route-match-user-uid-atom
               is-own-password-atom (r/reaction (= (:uid @@current-user-atom) @target-user-uid-atom))]
    [:<>
     [header-for-auth-attribute-edit "Pakeisti slaptažodį"]
     [render-password-reset-content
      temp-password-atom
      @is-own-password-atom
      form-state-atom]]))
