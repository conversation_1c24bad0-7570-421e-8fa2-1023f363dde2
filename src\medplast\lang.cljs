(ns medplast.lang
  (:require
   [clojure.string]
   [cljc.java-time.local-date :as local-date]
   [cljs.core.async :as async :refer [promise-chan put!]]))

(defn keywordized-int-to-int [kw-int]
  (when kw-int
    (let [intgr (-> kw-int name parse-long)]
      intgr)))

(defn sort-map-keys-by [key-fn m]
  (let [compare-fn #(compare (key-fn %1) (key-fn %2))]
    (into (sorted-map-by compare-fn) m)))

(defn swap-key [m key-to-find key-to-replace-with]
  (update-keys m #(if (= % key-to-find) key-to-replace-with %)))

(defn int-to-keywordized-int [i]
  (-> i str keyword))

(defn map-keywordized-int [map-fn kw-int]
  (when kw-int
    (-> kw-int keywordized-int-to-int map-fn int-to-keywordized-int)))

(defn index-using [xs key-fn]
  (->>
   xs
   (map (fn [x] [(key-fn x) x]))
   (into {})))

(defn- error? [x] (instance? js/Error x))

(defn <?js-promise-to-chan [js-promise]
  (let [c (promise-chan)
        handle-success!
        (fn [result]
          ; NOTE can't put nil on a channel
          (let [result (if (nil? result)
                         ::void-success
                         result)]
            #_(js/console.log "<?js-promise-to-chan handle-success!")
            (put! c result)))
        handle-error!
        (fn [error]
          ; (js/console.log "<?js-promise-to-chan handle-error!" error)
          (if (error? error)
            (put! c error)
            ; if not a formal error, just log it; otherwise, it
            ; would be interpreted as success output
            (js/console.error "error not js/Error, just logging:" error)))
        handle-finally
        (fn []
          #_(js/console.log "<?js-promise-to-chan cleanup"))]
    (-> js-promise
        (.then handle-success!)
        (.catch handle-error!)
        (.finally handle-finally))
    c))

(defn parse-local-date [local-date-str]
  (local-date/parse local-date-str))

(defn- throw-when-err
  "If e is an error, throws it. Otherwise, returns it."
  [e]
  (when (instance? js/Error e) (throw e))
  e)

(defmacro <?
   "Like cljs.core.async/<!, but throws errors."
   [ch]
     `(medplast.lang/throw-when-err (cljs.core.async/<! ~ch)))

(defmacro go-try
  "Like cljs.core.async/go, but catches the first thrown error and returns it.
   
   Example:

   ```
   (defn read-both [ch-a ch-b]
     (go-try
       (let [a (<? ch-a)
             b (<? ch-b)]
         [a b])))
   ```

   This example function returns a channel conveying either a vector of a and b or one 
   of the errors conveyed by ch-a or ch-b. It will never read from ch-b if ch-a 
   returns an error.

   From: https://github.com/alexanderkiel/async-error/blob/master/src/async_error/core.cljc
   "
   [& body]
  `(async/go
     (try
       ~@body
       (catch js/Error e#
         e#))))