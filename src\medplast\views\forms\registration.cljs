(ns medplast.views.forms.registration
  (:require
   [medplast.views.forms.core :refer [form input labeled]]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.firebase :as fb]
   [medplast.cloud-fn :as cloud-fn]
   [reagent.core :as r]
   [medplast.lang :refer-macros [go-try <?]]
   [cljs.core.async :refer [go <!]]))

(def initial-state
  {:email ""
   :phone-number ""
   :display-name ""})

(defn- <?handle-valid-submit!
  "Handles form submission by calling the createNewUser cloud function."
  [form-state-atom temp-password-atom]
  (go-try
   (let [{:keys [email phone-number display-name]} @form-state-atom
         response (<! (cloud-fn/<?create-new-user! email display-name phone-number))
         temp-password (-> response (aget "data") (aget "temporaryPassword"))]
     (println "<?handle-valid-submit! response" response)
     (reset! temp-password-atom temp-password)
     (js/console.log "User created successfully with temporary password."))))

(defn registration-form
  "Renders the registration form."
  [form-state-atom temp-password-atom]
  [form
   {:state-atom form-state-atom
    :<?valid-fn (fn [state-atom]
                  (<?handle-valid-submit! state-atom temp-password-atom))}
   [labeled {:label "El. paštas"}
    [input
     {:state-key :email
      :input-type :email
      :state-atom form-state-atom
      :required true}]]
   [labeled {:label "Telefono numeris (E.164 formatu, pvz., +370...)"}
    [input
     {:state-key :phone-number
      :input-type :tel
      :state-atom form-state-atom
      :required true}]]
   [labeled {:label "Suteiktas vardas"}
    [input
     {:state-key :display-name
      :input-type :str
      :state-atom form-state-atom
      :required false}]]])

(defn registration-page
  "Main page component for user registration."
  []
  (r/with-let [form-state-atom (r/atom initial-state)
               temp-password-atom (r/atom nil)]
    [:<>
     [header "Vartotojo registracija"
      {:on-click views-core/go-back!
       :text "Atgal"}]
     (if @temp-password-atom
       [:div {:class "mt-8"}
        [:p {:class "text-green-600"} "Vartotojas sėkmingai sukurtas!"]
        [:p "Laikinas slaptažodis: "
         [:strong @temp-password-atom]]
        [:p "Prašome perduoti šį slaptažodį vartotojui pirmam prisijungimui."]
        [:p "Šis slaptažodis daugiau nebus rodomas."]]
       [registration-form form-state-atom temp-password-atom])]))