(ns medplast.views.grids.sale
  (:require
   [clojure.string]
   [medplast.state :as state]
   [medplast.views.core :as views-core :refer [header]]
   [medplast.views.grids.core :as grids-core :refer [table]]
   [medplast.firestore :as firestore]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]
   [cljc.java-time.local-date :as local-date]
   [cljc.java-time.temporal.chrono-unit :as chrono-unit]
   ))

(defn make-action-cell-renderer [user]
  (grids-core/make-action-cell-renderer
   user
   (fn [doc]
     (let [doc-id (firestore/get-doc-id doc)
           user-uid (views-core/get-user-uid-from-sale doc)
           ; assigned-to-rendering-user (= (:assigned-to doc) (:uid user))
           ]
       [{:href (views-core/get-href-for-sale doc)
         :text "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
        {:href (rfe/href
                :medplast.routes/sale-edit
                {:user user-uid :id doc-id})
         :text "Redaguoti"}
        ; {:on-click (partial <?set-active-sale! doc-id false)
        ;  :text "Deactivate"
        ;  :when-fn #(:active doc)}
        ; {:on-click (partial <?set-active-sale! doc-id true)
        ;  :text "Activate"
        ;  :when-fn #(not (:active doc))}
        ; (when assigned-to-rendering-user
        {:href (rfe/href :medplast.routes/sale-replace
                         {:user user-uid :id doc-id})
         :text "Pakeisti nauju"}]))))

(defn stock-cell-renderer-component [& {:keys [products-to-quantity]}]
  [:div {:class "text-balance divide-y"}
   (for [[product quantity] products-to-quantity]
     (let [doc-id (firestore/get-doc-id product)
           unique-key (or doc-id (random-uuid))
           product-href (views-core/get-href-for-product product)
           product-label [views-core/product-label-with-type product]
           link [views-core/href-link product-href product-label]]
       ^{:key unique-key}
       [:div {:class "flex border-black/[.05]"}
        [:div {:class "flex-1 [word-wrap:break-word]"}
         (if product
           link
           [:span "Kraunama"])]
        [:div {:class "flex items-center w-10 text-right"} quantity]]))])

(defn make-stock-cell-renderer [id-to-product-map]
  (fn [params]
    (let [doc (grids-core/get-row-data params)
          stock (:stock doc)
          get-product-by-id (fn [kw-id] (get id-to-product-map (grids-core/safer-name kw-id)))
          products-to-quantity (update-keys stock get-product-by-id)]
      (r/as-element
       [stock-cell-renderer-component
        :products-to-quantity products-to-quantity]))))

#_(defn safe-name [input] (name (or input "")))

(defn calculate-expires-in-days [sale]
  (let [stock-expiration-date (local-date/parse (:stock-expiration-date sale))
        now  (local-date/now)
        expires-in-days (chrono-unit/between chrono-unit/days now stock-expiration-date)]
    expires-in-days))

(defn is-stock-expired [expires-in] (< expires-in 0))

(defn is-stock-soon-to-expire [expires-in]
  (and (<= expires-in 7) (not (is-stock-expired expires-in))))

(defn document-is-active [doc] (.-active doc))

(defn make-col-defs [{:keys [user id-to-product-map]}]
  (->>
   [{:field "actions"
     :headerName "Veiksmai"
     :cellRenderer (make-action-cell-renderer user)
     :sortable false
     :autoHeight true
     :wrapText true
     :width 280}
    (when (-> user :admin)
      {:field "salesperson"
       :headerName "Vartotojas"
       :cellRenderer grids-core/user-cell-renderer
       :valueFormatter grids-core/user-value-formatter
       :autoHeight false
       :wrapText false
       :filterParams
       #js {:filteroptions #js ["contains"]
            :textFormatter grids-core/user-text-formatter}
       :filter true})
    {:field "patient" :headerName "Pacientas"
     :cellRenderer grids-core/patient-cell-renderer
     :valueFormatter grids-core/patient-value-formatter
     :filterParams
     #js {:filteroptions #js ["contains"]
          :textFormatter grids-core/patient-text-formatter}
     :filter true
     :flex 1
     :minWidth 150
     :maxWidth 250
     :autoHeight true
     :wrapText true
          ;:sortable false
     }
    {:field "stock" :headerName "Prekės" :autoHeight true
     :cellRenderer (make-stock-cell-renderer id-to-product-map)
     :flex 1
     :minWidth 300
     :maxWidth 500
     :sortable false}
    {:field "comments" :headerName "Komentarai" :width 400
     :cellRenderer grids-core/compacted-long-text-cell-renderer
     :autoHeight true
     :filter true
     :filterParams #js {:filteroptions #js ["contains"]}
     :wrapText true}
    {:field "sale-date" :headerName "Pardavimo data"
     :width 150
     :filter true
     :autoHeaderHeight true
     :wrapHeaderText true}
    {:field "stock-expiration-date" :headerName "Prekių pasibaigimo data"
     :width 150
     :filter true
     :autoHeaderHeight true
     :wrapHeaderText true}
        ; NOTE does not reload the definition of today automatically, table must be reloaded
    {:field "stock-expires-in" :headerName "Prekės baigiasi už (dienų)"
    ; TODO only style when active
     :cellClassRules {"bg-green-600/20" #(and (document-is-active (.-data %)) (is-stock-soon-to-expire (.-value %)))
                      "bg-red-600/20" #(and (document-is-active (.-data %)) (is-stock-expired (.-value %)))}
     :autoHeaderHeight true
     :filter true
     :wrapHeaderText true
     :width 110}
    {:field "active" :headerName "Aktyvus?"
     :cellRenderer grids-core/boolean-cell-renderer
     :cellClassRules grids-core/active-cell-class-rules
     :width 150}
    {:field "invalid" :headerName "Klaidingas?"
     :cellRenderer grids-core/boolean-cell-renderer
     :cellClassRules grids-core/invalid-cell-class-rules
     :width 150}]
   (filter some?)))

(def initial-column-config
  [; {:colId "invalid", :sort "asc", :sortIndex 0}
   {:colId "active", :sort "desc", :sortIndex 1}
   {:colId "sale-date", :sort "desc", :sortIndex 2}
   {:colId "stock-expiration-date", :sort "asc", :sortIndex 3}])

(defn get-salesperson-by-sale [id-to-user-map sale]
  (let [doc-path (firestore/get-doc-path sale)
        user-id (second doc-path)]
    (get id-to-user-map user-id)))

(defn assoc-patient [id-to-patient-map row]
  (let [doc row
        patient-id (:patient doc)
        get-patient-by-id (fn [kw-id] (get id-to-patient-map (grids-core/safer-name kw-id)))
        patient (get-patient-by-id patient-id)]
    (assoc row :patient patient)))

(defn assoc-stock-expires-in [row]
  (assoc row :stock-expires-in (calculate-expires-in-days row)))

(defn assoc-salesperson [id-to-user-map row]
  (assoc row :salesperson
         (get-salesperson-by-sale id-to-user-map row)))

(defn process-table-data [id-to-user-map id-to-patient-map row-data]
  (->>
   row-data
   (map assoc-stock-expires-in)
   (map (partial assoc-salesperson id-to-user-map))
   (map (partial assoc-patient id-to-patient-map))))

(defn visible-sales-table []
  (r/with-let [logged-in-user-atom @(r/track state/get-logged-in-user-atom)
               row-data-atom @(r/track state/get-visible-sales-atom)
               id-to-user-map-atom @(r/track state/get-id-to-user-map-atom)
               id-to-patient-map-atom @(r/track state/get-id-to-patient-map-atom)
               row-data-atom
               (r/reaction
                (process-table-data
                 @id-to-user-map-atom
                 @id-to-patient-map-atom
                 @row-data-atom))
               id-to-product-map-atom @(r/track state/get-id-to-product-map-atom)
               col-defs-atom
               (r/reaction
                (make-col-defs {:id-to-product-map @id-to-product-map-atom
                                :id-to-patient-map @id-to-patient-map-atom
                                :user @logged-in-user-atom}))]
    [table {:row-data-atom row-data-atom
            :col-defs @col-defs-atom
            :initial-column-config initial-column-config}]))

(defn sale-list-page []
  (let [user-uid @@(r/track state/get-implied-user-uid-atom)]
    [:<>
     [header "Pardavimai"
      {:on-click views-core/go-back!
       :text "Atgal"}
      {:href (rfe/href :medplast.routes/sale-add {:user user-uid})
       :text "Naujas pardavimas"}]
     [visible-sales-table]]))

(defn patient-sales-table [patient-id]
  (let [row-data-atom
        @(r/track state/get-implied-user-sales-to-patient-atom patient-id)
        id-to-user-map-atom @(r/track state/get-id-to-user-map-atom)
        row-data-atom
        (r/reaction
         (let [id-to-patient-map nil]
           (process-table-data
            @id-to-user-map-atom
            id-to-patient-map
            @row-data-atom)))
        id-to-product-map-atom @(r/track state/get-id-to-product-map-atom)
        user-atom @(r/track state/get-logged-in-user-atom)
        col-defs-atom
        (r/reaction
         (filter
          #(not (contains? #{"patient"} (:field %)))
          (make-col-defs
           {:id-to-product-map @id-to-product-map-atom
            :user @user-atom})))]
    [table {:row-data-atom row-data-atom
            :col-defs @col-defs-atom
            ; turetu uztekti pakeisti filter-fn (turbut reiktu padaryti kad butu
            ; derefernciontas atomas) ir bus perrenderinta
            ;:filter-fn #(do (println %) true)
            :initial-column-config initial-column-config}]))

(defn user-sales-table [user-id]
  (let [user-atom @(r/track state/get-user-atom user-id)
        row-data-atom @(r/track state/get-user-sales-atom user-id)
        id-to-user-map-atom @(r/track state/get-id-to-user-map-atom)
        id-to-patient-map-atom @(r/track state/get-id-to-patient-map-atom)
        row-data-atom
        (r/reaction
         (process-table-data
          @id-to-user-map-atom
          @id-to-patient-map-atom
          @row-data-atom))
        id-to-product-map-atom @(r/track state/get-id-to-product-map-atom)
        col-defs-atom
        (r/reaction
         (make-col-defs {:id-to-product-map @id-to-product-map-atom
                         :id-to-patient-map @id-to-patient-map-atom
                         :user @user-atom}))]
    [table {:row-data-atom row-data-atom
            :col-defs @col-defs-atom
            :initial-column-config initial-column-config}]))
