(ns medplast.firebase
  (:require
   [env :as env]
   [clojure.string]
   [medplast.lang :refer-macros [<?] :refer [<?js-promise-to-chan go-try]]
   goog.dom
   goog.style
   ["firebase/app" :rename {initializeApp initialize-firebase-app} :as raw-firebase]
   ["firebase/auth" :as fb-auth :refer [PhoneAuthProvider PhoneMultiFactorGenerator RecaptchaVerifier]]
   [cljs.core.async :as async :refer [<! go]]
   [cljs-bean.core :refer [->clj ->js bean]]
   [reagent.core :as r]
   [reitit.frontend.easy :as rfe]))

(set! *warn-on-infer* true)

(def firebase-config
  (bean
   #js {"apiKey" "AIzaSyAPCoStlteJBMRNXSIn7Bk8TuFTVKydb0o"
        "authDomain" "medplast-baltic.firebaseapp.com"
        "projectId" "medplast-baltic"
        "storageBucket" "medplast-baltic.appspot.com"
        "messagingSenderId" "889094777274"
        "appId" "1:889094777274:web:80e677024cc5bb7e049583"
        "measurementId" "G-R7NWNESF3X"}))

(defonce firebase-app (initialize-firebase-app (->js firebase-config)))

(defonce firebase-auth
  (let [auth (fb-auth/getAuth firebase-app)]
    (when env/dev
      (let [emulator-conf (env/get :firebase-emulator)
            host (:host emulator-conf)
            port (:auth-port emulator-conf)]
        (fb-auth/connectAuthEmulator auth (str "http://" host ":" port))))
    auth))

; #_(defn doc-ref-js [& path-segments]
;   (when (every? some? path-segments)
;     (apply fs/doc firestore-instance path-segments)
;     ))
; NOTE https://stackoverflow.com/a/75192636/1714997
; firebase has ways to tell you when connection is lost and methods to try to reestablish connection

(defn- js-user-to-clj-user
  "Converts a Firebase JS User object to a Clojure map.
  Includes basic user info (email, uid) and MFA details.
  Returns nil if the input js-user is nil."
  [js-user]
  (when js-user
    (let [user {:email (.-email js-user)
                :uid (.-uid js-user)}
          phone-number (.-phoneNumber js-user)
          user (if phone-number
                 (assoc user :phone-number phone-number)
                 user)
          display-name (.-displayName js-user)
          user (if display-name
                 (assoc user :display-name display-name)
                 user)
          ;; Convert JS multiFactor object and enrolledFactors array to Clojure
          mfa-info-js (.-multiFactor js-user)
          mfa-info-clj (when mfa-info-js (-> mfa-info-js ->clj))
          enrolled-factors-js (when mfa-info-js (.-enrolledFactors mfa-info-js))
          enrolled-factors-clj (when enrolled-factors-js (-> enrolled-factors-js ->clj))]
      ;; Associate converted MFA info with the user map
      (cond-> user
        mfa-info-clj (assoc :mfa-info mfa-info-clj)
        enrolled-factors-clj (assoc :mfa-enrolled-factors enrolled-factors-clj))))) ;; Use a new key for the converted array

(defn <?auth-ready
  "Returns a core.async channel that will yield when the Firebase Auth state is initialized.
  This is useful to wait for the initial authentication state to be determined before proceeding,
  especially on app startup."
  []
  (let [promise (.authStateReady firebase-auth)
        c (<?js-promise-to-chan promise)]
    c))

(defn- <!get-claims-from-user-js
  "Fetches custom claims for a given Firebase JS User object.
  Returns a go block channel that yields the claims as a Clojure map."
  [user-js]
  (go
    (when user-js
      (let [token-result-promise (.getIdTokenResult user-js)
            token-result-c (<?js-promise-to-chan token-result-promise)
            token-result (<! token-result-c)
            claims (-> (.-claims token-result) ->clj)]
        claims))))

(defn get-current-user-subscription-components
  "Sets up a subscription to the Firebase Auth state changes.
  Returns a vector containing an unsubscribe function and a Reagent atom.
  The atom will be updated with the current user's details (including custom claims like admin/active status)
  whenever the authentication state changes (login, logout, etc.)."
  []
  (let [a (r/atom nil)
        unsubscribe-fn
        (fb-auth/onAuthStateChanged
         firebase-auth
         (fn [user-js]
           (go
             (let [claims (<! (<!get-claims-from-user-js user-js))
                   admin (:admin claims)
                   active (:active claims)
                   ; TODO is kebab-case right here?
                   must-reset-password (:mustResetPassword claims)
                   user (js-user-to-clj-user user-js)
                   user (when user
                          (assoc user
                                 :admin admin
                                 :active active
                                 :must-reset-password must-reset-password))]
               (reset! a user)))))]
    [unsubscribe-fn a]))

(defn get-auth
  "Returns the Firebase Auth instance."
  []
  firebase-auth)

(defn <?do-email-login!
  "Attempts to sign in a user with email and password.
  Returns a go block channel that yields the UserCredential on success.
  Catches and returns the specific 'auth/multi-factor-auth-required' error
  if MFA is required, allowing the caller to handle the MFA flow."
  [email password]
  (go ; Changed to go block to handle potential error
    (try
      (let [email (clojure.string/trim email)
            js-promise (fb-auth/signInWithEmailAndPassword firebase-auth email password)
            user-credential (<! (<?js-promise-to-chan js-promise))]
        user-credential) ; Return user credential on success
      (catch js/Error e
        (if (= (.-code e) "auth/multi-factor-auth-required")
          e ; Return the error object if it's the MFA error
          (throw e)))))) ; Re-throw other errors

(defn <?logout
  "Signs out the current user.
  Returns a channel that yields when the sign-out is complete."
  []
  (->
   firebase-auth
   fb-auth/signOut
   <?js-promise-to-chan))

(defn <logout-user!
  "Signs out the current user and redirects to the login page.
   This is a convenience function that combines <?logout with navigation."
  []
  (go
   (try (<? (<?logout))
        (finally (rfe/replace-state :medplast.routes/login)))))

(defn get-recaptcha-verifier
  "Creates a Firebase RecaptchaVerifier instance.
  Used for verifying phone numbers, especially in MFA flows.
  `container-id` is the ID of the DOM element to render the recaptcha.
  `options` is a JS object for configuration (size, callbacks, etc.)."
  [container-id options]
  ; container-id: string ID of the DOM element
  ; options: JS object for size, callbacks etc. e.g., #js {:size "invisible", :callback fn, :expired-callback fn}
  (RecaptchaVerifier. firebase-auth container-id options)) ; Use imported RecaptchaVerifier

(defn <?verify-phone-number-for-mfa!
  "Initiates phone number verification specifically for resolving an MFA sign-in challenge.
  `resolver` is the `MultiFactorResolver` from the MFA required error.
  `hint` is the specific `PhoneMultiFactorInfo` hint from the resolver's hints.
  `recaptcha-verifier` is an instance of `RecaptchaVerifier`.
  Returns a channel that yields the verification ID needed to complete the MFA sign-in."
  [resolver hint recaptcha-verifier]
  ; resolver: The MultiFactorResolver from the MFA error
  ; hint: The specific PhoneMultiFactorInfo hint
  ; recaptcha-verifier: Instance of RecaptchaVerifier
  (let [phone-auth-provider (PhoneAuthProvider. firebase-auth) ; Use imported PhoneAuthProvider
        phone-info-options #js {:multiFactorHint hint :session (.-session resolver)}] ; Use hint and session
    (-> (.verifyPhoneNumber phone-auth-provider phone-info-options recaptcha-verifier)
        <?js-promise-to-chan))) ; Returns verificationId

(defn <?resolve-mfa-signin!
  "Completes the multi-factor sign-in process after a phone number verification.
  `resolver` is the `MultiFactorResolver` from the MFA required error.
  `verification-id` is the ID received after sending the SMS code.
  `sms-code` is the code entered by the user.
  Returns a channel that yields the `UserCredential` on successful sign-in."
  [resolver verification-id sms-code]
  ; resolver: The MultiFactorResolver
  ; verification-id: The ID received after sending SMS
  ; sms-code: The code entered by the user
  (let [credential (.credential PhoneAuthProvider verification-id sms-code)
        assertion (.assertion PhoneMultiFactorGenerator credential)]
    (-> (.resolveSignIn ^js resolver assertion)
        <?js-promise-to-chan))) ; Returns UserCredential on success